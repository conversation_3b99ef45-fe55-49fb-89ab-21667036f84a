{"name": "duplicate-finder", "displayName": "Duplicate Finder", "description": "Smart duplicate code detection across your entire codebase", "version": "0.0.1", "publisher": "your-publisher-name", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Linters"], "keywords": ["duplicate", "code", "detection", "refactor", "quality"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "duplicateFinder.scanDocument", "title": "Scan Document for Duplicate Lines", "category": "Duplicate Finder"}, {"command": "duplicateFinder.clearHighlights", "title": "Clear Duplicate Highlights", "category": "Duplicate Finder"}], "menus": {"commandPalette": [{"command": "duplicateFinder.scanDocument"}, {"command": "duplicateFinder.clearHighlights"}]}, "configuration": {"title": "Duplicate Finder", "properties": {"duplicateFinder.autoScan": {"type": "boolean", "default": true, "description": "Automatically scan for duplicate lines when document changes"}, "duplicateFinder.ignoreWhitespace": {"type": "boolean", "default": true, "description": "Ignore whitespace-only lines when detecting duplicates"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@types/mocha": "^10.0.1", "@types/glob": "^8.1.0", "@vscode/test-electron": "^2.3.0", "eslint": "^8.39.0", "@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "glob": "^8.1.0", "mocha": "^10.2.0", "typescript": "^4.9.4"}, "dependencies": {}}