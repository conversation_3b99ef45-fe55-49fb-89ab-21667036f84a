{"name": "duplicate-finder", "displayName": "Duplicate Finder", "description": "Smart duplicate code detection across your entire codebase", "version": "0.0.1", "publisher": "your-publisher-name", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Linters"], "keywords": ["duplicate", "code", "detection", "refactor", "quality"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "duplicateFinder.scanDocument", "title": "Scan Document for Duplicate Lines", "category": "Duplicate Finder"}, {"command": "duplicateFinder.clearHighlights", "title": "Clear Duplicate Highlights", "category": "Duplicate Finder"}, {"command": "duplicateFinder.navigateToGroup", "title": "Navigate to Duplicate Group", "category": "Duplicate Finder"}, {"command": "duplicateFinder.nextInstance", "title": "Next Duplicate Instance", "category": "Duplicate Finder", "icon": "$(arrow-right)"}, {"command": "duplicateFinder.prevInstance", "title": "Previous Duplicate Instance", "category": "Duplicate Finder", "icon": "$(arrow-left)"}, {"command": "duplicateFinder.removeLine", "title": "Remove Duplicate Line", "category": "Duplicate Finder", "icon": "$(trash)"}, {"command": "duplicateFinder.keepLine", "title": "Keep This Line", "category": "Duplicate Finder", "icon": "$(check)"}], "views": {"explorer": [{"id": "duplicate<PERSON><PERSON><PERSON>ie<PERSON>", "name": "Duplicate Lines"}]}, "keybindings": [{"command": "duplicateFinder.nextInstance", "key": "ctrl+shift+]", "mac": "cmd+shift+]", "when": "editorTextFocus"}, {"command": "duplicateFinder.prevInstance", "key": "ctrl+shift+[", "mac": "cmd+shift+[", "when": "editorTextFocus"}], "menus": {"view/title": [{"command": "duplicateFinder.scanDocument", "when": "view == duplicateFinderView", "group": "navigation"}, {"command": "duplicateFinder.clearHighlights", "when": "view == duplicateFinderView", "group": "navigation"}], "commandPalette": [{"command": "duplicateFinder.scanDocument"}, {"command": "duplicateFinder.clearHighlights"}, {"command": "duplicateFinder.nextInstance"}, {"command": "duplicateFinder.prevInstance"}, {"command": "duplicateFinder.removeLine"}, {"command": "duplicateFinder.keepLine"}]}, "configuration": {"title": "Duplicate Finder", "properties": {"duplicateFinder.autoScan": {"type": "boolean", "default": true, "description": "Automatically scan for duplicate lines when document changes"}, "duplicateFinder.ignoreWhitespace": {"type": "boolean", "default": true, "description": "Ignore whitespace-only lines when detecting duplicates"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@types/mocha": "^10.0.1", "@types/glob": "^8.1.0", "@vscode/test-electron": "^2.3.0", "eslint": "^8.39.0", "@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "glob": "^8.1.0", "mocha": "^10.2.0", "typescript": "^4.9.4"}, "dependencies": {}}