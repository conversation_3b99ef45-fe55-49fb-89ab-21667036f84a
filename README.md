# Duplicate Finder

A simple VS Code extension that detects and highlights duplicate lines in the currently open document.

## Features

- **Real-time Detection**: Automatically detects duplicate lines as you type
- **Visual Highlighting**: Each group of duplicate lines gets a different background color
- **Duplicate Management Panel**: Side panel showing all duplicate groups with instance counts
- **Navigation Controls**: Click to navigate between duplicate instances like Git changes
- **Line Management**: Remove or keep individual duplicate lines with one click
- **Keyboard Shortcuts**: Quick navigation with `Ctrl+Shift+[` and `Ctrl+Shift+]`
- **Clean Interface**: Intuitive workflow for managing duplicates
- **Performance**: Lightweight and fast, works on any file type

## Installation

1. Open VS Code
2. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac) to open the Command Palette
3. Type "Extensions: Install from VSIX"
4. Select the generated `.vsix` file

## Usage

### Automatic Detection

The extension automatically scans the currently open document and highlights duplicate lines:
- **Different Colors**: Each group of duplicate lines gets a unique background color
- **Real-time Updates**: Highlights update as you edit the document
- **Overview Ruler**: Duplicate lines are also marked in the overview ruler on the right

### Duplicate Management Panel

The extension adds a "Duplicate Lines" panel to the Explorer sidebar that shows:
- **Duplicate Groups**: Each unique duplicate line with instance count
- **Click to Navigate**: Click any group to start navigating through instances
- **Instance Counter**: Shows current position (e.g., "Duplicate 2 of 4")

### Navigation & Management

When you click on a duplicate group, you get navigation controls:
- **Previous/Next Buttons**: Navigate between instances of the same duplicate
- **Remove Line**: Delete the current duplicate line
- **Keep Line**: Mark this line as intentional (removes from duplicate list)
- **Keyboard Shortcuts**:
  - `Ctrl+Shift+[` / `Cmd+Shift+[` - Previous instance
  - `Ctrl+Shift+]` / `Cmd+Shift+]` - Next instance

### Manual Commands

- **Scan Document**: `Ctrl+Shift+P` → "Duplicate Finder: Scan Document for Duplicate Lines"
- **Clear Highlights**: `Ctrl+Shift+P` → "Duplicate Finder: Clear Duplicate Highlights"

### How It Works

1. Open any file in VS Code
2. The extension automatically scans for duplicate lines
3. Identical lines are highlighted and listed in the "Duplicate Lines" panel
4. Click on any duplicate group to start navigating through instances
5. Use "Remove Line" or "Keep Line" buttons to manage duplicates
6. Edit the document and see highlights update in real-time

## Configuration

Configure the extension through VS Code settings:

```json
{
  "duplicateFinder.autoScan": true,
  "duplicateFinder.ignoreWhitespace": true
}
```

### Settings

- `autoScan`: Automatically scan for duplicate lines when document changes (default: true)
- `ignoreWhitespace`: Ignore whitespace-only lines when detecting duplicates (default: true)

## Colors

The extension uses 8 different background colors to distinguish duplicate groups:

- **Red**: `#ff000020`
- **Green**: `#00ff0020`
- **Blue**: `#0000ff20`
- **Yellow**: `#ffff0020`
- **Magenta**: `#ff00ff20`
- **Cyan**: `#00ffff20`
- **Orange**: `#ffa50020`
- **Purple**: `#80008020`

If you have more than 8 groups of duplicates, the colors will cycle through again.

## Development

### Prerequisites

- Node.js 16.x or higher
- VS Code 1.74.0 or higher

### Building

```bash
npm install
npm run compile
```

### Running

1. Open the project in VS Code
2. Press `F5` to launch a new Extension Development Host window
3. Test the extension in the new window

## License

MIT License - see LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
