# Duplicate Finder

A smart VS Code extension that detects and helps manage duplicate code across your entire codebase.

## Features

- **Smart Detection**: Uses AST-based analysis to detect meaningful code duplication
- **Language Agnostic**: Supports JavaScript, TypeScript, Python, Java, C#, C++, C, Go, Rust, and PHP
- **Visual Indicators**: Highlights duplicate code with decorations and hover information
- **Side Panel**: Tree view showing all duplicate groups with easy navigation
- **Configurable**: Adjustable similarity thresholds and exclusion patterns
- **Real-time**: Automatically scans for duplicates when files change

## Installation

1. Open VS Code
2. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac) to open the Command Palette
3. Type "Extensions: Install from VSIX"
4. Select the generated `.vsix` file

## Usage

### Commands

- **Scan for Duplicates**: `Ctrl+Shift+P` → "Duplicate Finder: Scan for Duplicates"
- **Clear Highlights**: `Ctrl+Shift+P` → "Duplicate Finder: Clear Duplicate Highlights"
- **Show Duplicate Panel**: `Ctrl+Shift+P` → "Duplicate Finder: Show Duplicate Panel"

### Side Panel

The extension adds a "Duplicate Code" view to the Explorer panel that shows:
- Duplicate groups organized by similarity
- Individual duplicate locations with file and line information
- Click any item to navigate to that location

### Visual Indicators

Duplicate code is highlighted with:
- Background color highlighting
- Border decoration
- Overview ruler markers
- Hover tooltips with duplicate information and navigation links

## Configuration

Configure the extension through VS Code settings:

```json
{
  "duplicateFinder.minLines": 5,
  "duplicateFinder.similarityThreshold": 0.8,
  "duplicateFinder.excludePatterns": [
    "**/node_modules/**",
    "**/dist/**",
    "**/build/**",
    "**/*.min.js"
  ],
  "duplicateFinder.enabledLanguages": [
    "javascript",
    "typescript",
    "python",
    "java",
    "csharp",
    "cpp",
    "c",
    "go",
    "rust",
    "php"
  ],
  "duplicateFinder.autoScan": true
}
```

### Settings

- `minLines`: Minimum number of lines to consider as duplicate (default: 5)
- `similarityThreshold`: Similarity threshold from 0.1 to 1.0 (default: 0.8)
- `excludePatterns`: File patterns to exclude from scanning
- `enabledLanguages`: Programming languages to analyze
- `autoScan`: Automatically scan when files change (default: true)

## Smart Detection

The extension uses sophisticated algorithms to avoid false positives:

- **AST Analysis**: Analyzes code structure, not just text
- **Trivial Pattern Filtering**: Ignores common patterns like imports, simple assignments
- **Context Awareness**: Considers code context and significance
- **Configurable Thresholds**: Adjustable sensitivity based on code length and complexity

## Development

### Prerequisites

- Node.js 16.x or higher
- VS Code 1.74.0 or higher

### Building

```bash
npm install
npm run compile
```

### Running

1. Open the project in VS Code
2. Press `F5` to launch a new Extension Development Host window
3. Test the extension in the new window

## License

MIT License - see LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
