# Duplicate Finder

A simple VS Code extension that detects and highlights duplicate lines in the currently open document.

## Features

- **Real-time Detection**: Automatically detects duplicate lines as you type
- **Visual Highlighting**: Each group of duplicate lines gets a different background color
- **Instant Feedback**: See duplicates immediately highlighted in your code
- **Clean Interface**: No complex configuration - just works out of the box
- **Performance**: Lightweight and fast, works on any file type

## Installation

1. Open VS Code
2. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac) to open the Command Palette
3. Type "Extensions: Install from VSIX"
4. Select the generated `.vsix` file

## Usage

### Automatic Detection

The extension automatically scans the currently open document and highlights duplicate lines:
- **Different Colors**: Each group of duplicate lines gets a unique background color
- **Real-time Updates**: Highlights update as you edit the document
- **Overview Ruler**: Duplicate lines are also marked in the overview ruler on the right

### Manual Commands

- **Scan Document**: `Ctrl+Shift+P` → "Duplicate Finder: Scan Document for Duplicate Lines"
- **Clear Highlights**: `Ctrl+Shift+P` → "Duplicate Finder: Clear Duplicate Highlights"

### How It Works

1. Open any file in VS Code
2. The extension automatically scans for duplicate lines
3. Identical lines (ignoring whitespace-only lines) are highlighted with the same color
4. Different groups of duplicates get different colors
5. Edit the document and see highlights update in real-time

## Configuration

Configure the extension through VS Code settings:

```json
{
  "duplicateFinder.autoScan": true,
  "duplicateFinder.ignoreWhitespace": true
}
```

### Settings

- `autoScan`: Automatically scan for duplicate lines when document changes (default: true)
- `ignoreWhitespace`: Ignore whitespace-only lines when detecting duplicates (default: true)

## Colors

The extension uses 8 different background colors to distinguish duplicate groups:

- **Red**: `#ff000020`
- **Green**: `#00ff0020`
- **Blue**: `#0000ff20`
- **Yellow**: `#ffff0020`
- **Magenta**: `#ff00ff20`
- **Cyan**: `#00ffff20`
- **Orange**: `#ffa50020`
- **Purple**: `#80008020`

If you have more than 8 groups of duplicates, the colors will cycle through again.

## Development

### Prerequisites

- Node.js 16.x or higher
- VS Code 1.74.0 or higher

### Building

```bash
npm install
npm run compile
```

### Running

1. Open the project in VS Code
2. Press `F5` to launch a new Extension Development Host window
3. Test the extension in the new window

## License

MIT License - see LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
