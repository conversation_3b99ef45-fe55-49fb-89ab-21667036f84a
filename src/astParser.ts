import * as vscode from 'vscode';
import { ASTNode, LanguageConfig, ParsedFile } from './types';

// Note: Tree-sitter imports would be here in a real implementation
// For now, we'll use a simplified AST approach

export class ASTParser {
  private languageConfigs: Map<string, LanguageConfig> = new Map();

  constructor() {
    this.initializeLanguageConfigs();
  }

  private initializeLanguageConfigs(): void {
    // JavaScript/TypeScript
    this.languageConfigs.set('javascript', {
      id: 'javascript',
      extensions: ['.js', '.jsx'],
      trivialPatterns: [
        /^const\s+\w+\s*=\s*document\./,
        /^import\s+.*from\s+['"].*['"];?$/,
        /^export\s+.*$/,
        /^\/\/.*$/,
        /^\/\*.*\*\/$/
      ],
      blockMinLines: 3
    });

    this.languageConfigs.set('typescript', {
      id: 'typescript',
      extensions: ['.ts', '.tsx'],
      trivialPatterns: [
        /^const\s+\w+\s*=\s*document\./,
        /^import\s+.*from\s+['"].*['"];?$/,
        /^export\s+.*$/,
        /^interface\s+\w+\s*{$/,
        /^type\s+\w+\s*=.*$/
      ],
      blockMinLines: 3
    });

    // Python
    this.languageConfigs.set('python', {
      id: 'python',
      extensions: ['.py'],
      trivialPatterns: [
        /^import\s+.*$/,
        /^from\s+.*import\s+.*$/,
        /^#.*$/,
        /^""".*"""$/,
        /^def\s+__\w+__\(.*\):$/
      ],
      blockMinLines: 3
    });

    // Java
    this.languageConfigs.set('java', {
      id: 'java',
      extensions: ['.java'],
      trivialPatterns: [
        /^import\s+.*$/,
        /^package\s+.*$/,
        /^\/\/.*$/,
        /^\/\*.*\*\/$/,
        /^public\s+class\s+\w+.*{$/
      ],
      blockMinLines: 4
    });

    // Add more languages as needed
  }

  public async parseFile(filePath: string): Promise<ParsedFile | null> {
    try {
      const document = await vscode.workspace.openTextDocument(filePath);
      const language = this.detectLanguage(filePath, document.languageId);
      
      if (!language) {
        return null;
      }

      const content = document.getText();
      const ast = this.parseContent(content, language);
      const blocks = this.extractCodeBlocks(content, language, filePath);

      return {
        filePath,
        language: language.id,
        ast,
        blocks
      };
    } catch (error) {
      console.error(`Error parsing file ${filePath}:`, error);
      return null;
    }
  }

  private detectLanguage(filePath: string, languageId: string): LanguageConfig | null {
    // First try by VS Code language ID
    if (this.languageConfigs.has(languageId)) {
      return this.languageConfigs.get(languageId)!;
    }

    // Fallback to file extension
    const extension = '.' + filePath.split('.').pop()?.toLowerCase();
    for (const config of this.languageConfigs.values()) {
      if (config.extensions.includes(extension)) {
        return config;
      }
    }

    return null;
  }

  private parseContent(content: string, language: LanguageConfig): ASTNode {
    // Simplified AST parsing - in a real implementation, this would use Tree-sitter
    const lines = content.split('\n');
    
    return {
      type: 'program',
      startPosition: { row: 0, column: 0 },
      endPosition: { row: lines.length - 1, column: lines[lines.length - 1]?.length || 0 },
      children: this.parseStatements(lines, language),
      text: content
    };
  }

  private parseStatements(lines: string[], language: LanguageConfig): ASTNode[] {
    const statements: ASTNode[] = [];
    let currentStatement: string[] = [];
    let startLine = 0;
    let braceCount = 0;
    let inString = false;
    let stringChar = '';

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      if (line === '' || this.isTrivialLine(line, language)) {
        if (currentStatement.length > 0) {
          statements.push(this.createStatementNode(currentStatement, startLine, i - 1));
          currentStatement = [];
        }
        continue;
      }

      if (currentStatement.length === 0) {
        startLine = i;
      }

      currentStatement.push(lines[i]);

      // Simple brace counting for block detection
      for (const char of line) {
        if (!inString && (char === '"' || char === "'" || char === '`')) {
          inString = true;
          stringChar = char;
        } else if (inString && char === stringChar) {
          inString = false;
          stringChar = '';
        } else if (!inString) {
          if (char === '{') braceCount++;
          else if (char === '}') braceCount--;
        }
      }

      // End statement on semicolon, closing brace, or specific patterns
      if (!inString && (
        line.endsWith(';') || 
        line.endsWith('}') || 
        (braceCount === 0 && currentStatement.length >= language.blockMinLines)
      )) {
        statements.push(this.createStatementNode(currentStatement, startLine, i));
        currentStatement = [];
        braceCount = 0;
      }
    }

    // Handle remaining statement
    if (currentStatement.length > 0) {
      statements.push(this.createStatementNode(currentStatement, startLine, lines.length - 1));
    }

    return statements;
  }

  private createStatementNode(lines: string[], startLine: number, endLine: number): ASTNode {
    return {
      type: 'statement',
      startPosition: { row: startLine, column: 0 },
      endPosition: { row: endLine, column: lines[lines.length - 1]?.length || 0 },
      text: lines.join('\n')
    };
  }

  private isTrivialLine(line: string, language: LanguageConfig): boolean {
    return language.trivialPatterns.some(pattern => pattern.test(line));
  }

  private extractCodeBlocks(content: string, language: LanguageConfig, filePath: string): any[] {
    const lines = content.split('\n');
    const blocks: any[] = [];
    let currentBlock: string[] = [];
    let startLine = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      if (line === '' || this.isTrivialLine(line, language)) {
        if (currentBlock.length >= language.blockMinLines) {
          blocks.push({
            content: currentBlock.join('\n'),
            startLine,
            endLine: i - 1,
            filePath,
            hash: this.hashContent(currentBlock.join('\n'))
          });
        }
        currentBlock = [];
        continue;
      }

      if (currentBlock.length === 0) {
        startLine = i;
      }
      currentBlock.push(lines[i]);
    }

    // Handle final block
    if (currentBlock.length >= language.blockMinLines) {
      blocks.push({
        content: currentBlock.join('\n'),
        startLine,
        endLine: lines.length - 1,
        filePath,
        hash: this.hashContent(currentBlock.join('\n'))
      });
    }

    return blocks;
  }

  private hashContent(content: string): string {
    // Simple hash function - in production, use a proper hash library
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  public normalizeCode(content: string, language: LanguageConfig): string {
    // Normalize code for better comparison
    return content
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/\/\/.*$/gm, '') // Remove single-line comments
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove multi-line comments
      .replace(/['"`]/g, '"') // Normalize quotes
      .trim();
  }
}
