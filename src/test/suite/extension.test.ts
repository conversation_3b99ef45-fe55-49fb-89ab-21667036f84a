import * as assert from 'assert';
import * as vscode from 'vscode';
import { SimilarityAnalyzer } from '../../similarityAnalyzer';
import { CodeBlock } from '../../types';

suite('Extension Test Suite', () => {
  vscode.window.showInformationMessage('Start all tests.');

  test('Similarity Analyzer Test', () => {
    const analyzer = new SimilarityAnalyzer();
    
    const block1: CodeBlock = {
      content: `function calculateSum(a, b) {
        const result = a + b;
        return result;
      }`,
      startLine: 1,
      endLine: 4,
      filePath: '/test/file1.js',
      hash: 'hash1'
    };

    const block2: CodeBlock = {
      content: `function calculateTotal(x, y) {
        const sum = x + y;
        return sum;
      }`,
      startLine: 10,
      endLine: 13,
      filePath: '/test/file2.js',
      hash: 'hash2'
    };

    const similarity = analyzer.analyzeSimilarity(block1, block2);
    
    assert.ok(similarity.similarity > 0.7, 'Similar functions should have high similarity');
    assert.ok(similarity.isSignificant, 'Similar functions should be flagged as significant');
  });

  test('Trivial Code Detection', () => {
    const analyzer = new SimilarityAnalyzer();
    
    const block1: CodeBlock = {
      content: `const element = document.getElementById('test');`,
      startLine: 1,
      endLine: 1,
      filePath: '/test/file1.js',
      hash: 'hash1'
    };

    const block2: CodeBlock = {
      content: `const button = document.getElementById('button');`,
      startLine: 5,
      endLine: 5,
      filePath: '/test/file2.js',
      hash: 'hash2'
    };

    const similarity = analyzer.analyzeSimilarity(block1, block2);
    
    assert.ok(!similarity.isSignificant, 'Trivial DOM queries should not be flagged as significant duplicates');
  });
});
