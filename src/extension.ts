import * as vscode from 'vscode';

// Simple tree item class
class DuplicateTreeItem extends vscode.TreeItem {
  constructor(
    public readonly label: string,
    public readonly collapsibleState: vscode.TreeItemCollapsibleState,
    public readonly command?: vscode.Command
  ) {
    super(label, collapsibleState);
    this.tooltip = this.label;
  }
}

// Simple tree data provider
class DuplicateTreeDataProvider implements vscode.TreeDataProvider<DuplicateTreeItem> {
  private _onDidChangeTreeData: vscode.EventEmitter<DuplicateTreeItem | undefined | null | void> = new vscode.EventEmitter<DuplicateTreeItem | undefined | null | void>();
  readonly onDidChangeTreeData: vscode.Event<DuplicateTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

  private items: DuplicateTreeItem[] = [];

  getTreeItem(element: DuplicateTreeItem): vscode.TreeItem {
    return element;
  }

  getChildren(element?: DuplicateTreeItem): Thenable<DuplicateTreeItem[]> {
    if (!element) {
      if (this.items.length === 0) {
        return Promise.resolve([
          new DuplicateTreeItem(
            'No duplicates found',
            vscode.TreeItemCollapsibleState.None,
            {
              command: 'duplicateFinder.scanWorkspace',
              title: 'Scan for Duplicates'
            }
          )
        ]);
      }
      return Promise.resolve(this.items);
    }
    return Promise.resolve([]);
  }

  addItem(label: string): void {
    this.items.push(new DuplicateTreeItem(label, vscode.TreeItemCollapsibleState.None));
    this._onDidChangeTreeData.fire();
  }

  clear(): void {
    this.items = [];
    this._onDidChangeTreeData.fire();
  }
}

export function activate(context: vscode.ExtensionContext) {
  console.log('Duplicate Finder extension is now active!');

  // Simple tree data provider
  const treeDataProvider = new DuplicateTreeDataProvider();
  vscode.window.registerTreeDataProvider('duplicateFinderView', treeDataProvider);

  // Register commands
  const scanCommand = vscode.commands.registerCommand('duplicateFinder.scanWorkspace', async () => {
    vscode.window.showInformationMessage('Scanning for duplicates...');

    // Simple demonstration - add some dummy items
    treeDataProvider.clear();
    treeDataProvider.addItem('Example duplicate 1');
    treeDataProvider.addItem('Example duplicate 2');

    vscode.window.showInformationMessage('Found 2 example duplicates');
  });

  const clearCommand = vscode.commands.registerCommand('duplicateFinder.clearHighlights', () => {
    treeDataProvider.clear();
    vscode.window.showInformationMessage('Cleared duplicate highlights');
  });

  const showPanelCommand = vscode.commands.registerCommand('duplicateFinder.showDuplicatePanel', async () => {
    await vscode.commands.executeCommand('duplicateFinderView.focus');
    vscode.window.showInformationMessage('Duplicate panel focused');
  });

  // Add subscriptions
  context.subscriptions.push(
    scanCommand,
    clearCommand,
    showPanelCommand
  );
}

export function deactivate() {
  // Clean up resources
}
