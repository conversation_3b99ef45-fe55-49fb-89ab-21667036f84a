import * as vscode from 'vscode';

// Define colors for different duplicate groups
const DUPLICATE_COLORS = [
  '#ff000020', // Red
  '#00ff0020', // Green
  '#0000ff20', // Blue
  '#ffff0020', // Yellow
  '#ff00ff20', // Magenta
  '#00ffff20', // Cyan
  '#ffa50020', // Orange
  '#80008020', // Purple
];

let decorationTypes: vscode.TextEditorDecorationType[] = [];
let currentEditor: vscode.TextEditor | undefined;

export function activate(context: vscode.ExtensionContext) {
  console.log('Duplicate Finder extension is now active!');

  // Initialize decoration types
  initializeDecorationTypes();

  // Register commands
  const scanCommand = vscode.commands.registerCommand('duplicateFinder.scanDocument', () => {
    scanCurrentDocument();
  });

  const clearCommand = vscode.commands.registerCommand('duplicateFinder.clearHighlights', () => {
    clearHighlights();
  });

  // Listen for active editor changes
  vscode.window.onDidChangeActiveTextEditor(editor => {
    currentEditor = editor;
    if (editor) {
      scanCurrentDocument();
    }
  });

  // Listen for document changes
  vscode.workspace.onDidChangeTextDocument(event => {
    if (currentEditor && event.document === currentEditor.document) {
      // Debounce the scanning to avoid too frequent updates
      setTimeout(() => scanCurrentDocument(), 500);
    }
  });

  // Set initial editor
  currentEditor = vscode.window.activeTextEditor;
  if (currentEditor) {
    scanCurrentDocument();
  }

  context.subscriptions.push(scanCommand, clearCommand);
}

function initializeDecorationTypes() {
  // Create decoration types for each color
  decorationTypes = DUPLICATE_COLORS.map(color =>
    vscode.window.createTextEditorDecorationType({
      backgroundColor: color,
      isWholeLine: true,
      overviewRulerColor: color.replace('20', '80'), // More opaque for ruler
      overviewRulerLane: vscode.OverviewRulerLane.Right
    })
  );
}

function scanCurrentDocument() {
  if (!currentEditor) {
    return;
  }

  const document = currentEditor.document;
  const text = document.getText();
  const lines = text.split('\n');

  // Clear previous highlights
  clearHighlights();

  // Find duplicate lines
  const duplicateGroups = findDuplicateLines(lines);

  // Apply highlights
  highlightDuplicateGroups(duplicateGroups);

  // Show status message
  const totalDuplicates = duplicateGroups.reduce((sum, group) => sum + group.lineNumbers.length, 0);
  if (duplicateGroups.length > 0) {
    vscode.window.showInformationMessage(
      `Found ${duplicateGroups.length} groups of duplicate lines (${totalDuplicates} total lines)`
    );
  }
}

function findDuplicateLines(lines: string[]): DuplicateGroup[] {
  const lineMap = new Map<string, number[]>();

  // Group lines by content (ignoring empty lines and whitespace-only lines)
  lines.forEach((line, index) => {
    const trimmedLine = line.trim();
    if (trimmedLine.length > 0) {
      if (!lineMap.has(trimmedLine)) {
        lineMap.set(trimmedLine, []);
      }
      lineMap.get(trimmedLine)!.push(index);
    }
  });

  // Filter to only include lines that appear more than once
  const duplicateGroups: DuplicateGroup[] = [];
  lineMap.forEach((lineNumbers, content) => {
    if (lineNumbers.length > 1) {
      duplicateGroups.push({
        content,
        lineNumbers,
        count: lineNumbers.length
      });
    }
  });

  // Sort by count (most duplicates first)
  duplicateGroups.sort((a, b) => b.count - a.count);

  return duplicateGroups;
}

function highlightDuplicateGroups(groups: DuplicateGroup[]) {
  if (!currentEditor) {
    return;
  }

  groups.forEach((group, groupIndex) => {
    const colorIndex = groupIndex % DUPLICATE_COLORS.length;
    const decorationType = decorationTypes[colorIndex];

    const ranges = group.lineNumbers.map(lineNumber =>
      new vscode.Range(lineNumber, 0, lineNumber, Number.MAX_SAFE_INTEGER)
    );

    currentEditor!.setDecorations(decorationType, ranges);
  });
}

function clearHighlights() {
  if (!currentEditor) {
    return;
  }

  decorationTypes.forEach(decorationType => {
    currentEditor!.setDecorations(decorationType, []);
  });
}

interface DuplicateGroup {
  content: string;
  lineNumbers: number[];
  count: number;
}

export function deactivate() {
  clearHighlights();
  decorationTypes.forEach(decorationType => decorationType.dispose());
}
