import * as vscode from 'vscode';

export interface CodeBlock {
  content: string;
  startLine: number;
  endLine: number;
  filePath: string;
  hash: string;
  astHash?: string;
  normalizedContent?: string;
}

export interface DuplicateGroup {
  id: string;
  blocks: CodeBlock[];
  similarity: number;
  lineCount: number;
  language: string;
}

export interface SimilarityResult {
  similarity: number;
  structuralSimilarity: number;
  textualSimilarity: number;
  isSignificant: boolean;
}

export interface LanguageConfig {
  id: string;
  extensions: string[];
  parser?: any;
  trivialPatterns: RegExp[];
  blockMinLines: number;
}

export interface DuplicateFinderConfig {
  minLines: number;
  similarityThreshold: number;
  excludePatterns: string[];
  enabledLanguages: string[];
  autoScan: boolean;
}

export interface ASTNode {
  type: string;
  startPosition: { row: number; column: number };
  endPosition: { row: number; column: number };
  children?: ASTNode[];
  text?: string;
}

export interface ParsedFile {
  filePath: string;
  language: string;
  ast: ASTNode;
  blocks: CodeBlock[];
}

export class DuplicateTreeItem extends vscode.TreeItem {
  constructor(
    public readonly label: string,
    public readonly collapsibleState: vscode.TreeItemCollapsibleState,
    public readonly duplicateGroup?: DuplicateGroup,
    public readonly codeBlock?: CodeBlock
  ) {
    super(label, collapsibleState);
    
    if (codeBlock) {
      this.command = {
        command: 'duplicateFinder.navigateToDuplicate',
        title: 'Navigate to Duplicate',
        arguments: [codeBlock]
      };
      this.tooltip = `${codeBlock.filePath}:${codeBlock.startLine}-${codeBlock.endLine}`;
      this.contextValue = 'duplicateBlock';
    } else if (duplicateGroup) {
      this.tooltip = `${duplicateGroup.blocks.length} duplicates, ${duplicateGroup.lineCount} lines, ${Math.round(duplicateGroup.similarity * 100)}% similar`;
      this.contextValue = 'duplicateGroup';
    }
  }
}

export interface DecorationInfo {
  range: vscode.Range;
  duplicateGroup: DuplicateGroup;
  codeBlock: CodeBlock;
}
