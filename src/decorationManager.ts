import * as vscode from 'vscode';
import { DuplicateGroup, DecorationInfo } from './types';

export class DecorationManager {
  private decorationType: vscode.TextEditorDecorationType;
  private decorations: Map<string, DecorationInfo[]> = new Map();

  constructor() {
    this.decorationType = vscode.window.createTextEditorDecorationType({
      backgroundColor: new vscode.ThemeColor('duplicateFinder.duplicateBackground'),
      borderWidth: '1px',
      borderStyle: 'solid',
      borderColor: new vscode.ThemeColor('duplicateFinder.duplicateBorder'),
      overviewRulerColor: new vscode.ThemeColor('duplicateFinder.duplicateOverviewRuler'),
      overviewRulerLane: vscode.OverviewRulerLane.Right,
      isWholeLine: true,
      after: {
        contentText: ' 🔄',
        color: new vscode.ThemeColor('duplicateFinder.duplicateIcon')
      }
    });

    // Listen for active editor changes to update decorations
    vscode.window.onDidChangeActiveTextEditor(editor => {
      if (editor) {
        this.updateEditorDecorations(editor);
      }
    });

    // Listen for text document changes to update decorations
    vscode.workspace.onDidChangeTextDocument(event => {
      const editor = vscode.window.visibleTextEditors.find(e => e.document === event.document);
      if (editor) {
        this.updateEditorDecorations(editor);
      }
    });
  }

  public updateDecorations(duplicateGroups: DuplicateGroup[]): void {
    // Clear existing decorations
    this.clearAllDecorations();

    // Create new decorations
    for (const group of duplicateGroups) {
      for (const block of group.blocks) {
        const filePath = block.filePath;
        
        if (!this.decorations.has(filePath)) {
          this.decorations.set(filePath, []);
        }

        const range = new vscode.Range(
          new vscode.Position(block.startLine, 0),
          new vscode.Position(block.endLine, Number.MAX_SAFE_INTEGER)
        );

        const decorationInfo: DecorationInfo = {
          range,
          duplicateGroup: group,
          codeBlock: block
        };

        this.decorations.get(filePath)!.push(decorationInfo);
      }
    }

    // Apply decorations to currently visible editors
    for (const editor of vscode.window.visibleTextEditors) {
      this.updateEditorDecorations(editor);
    }
  }

  public clearAllDecorations(): void {
    this.decorations.clear();
    
    // Clear decorations from all visible editors
    for (const editor of vscode.window.visibleTextEditors) {
      editor.setDecorations(this.decorationType, []);
    }
  }

  private updateEditorDecorations(editor: vscode.TextEditor): void {
    const filePath = editor.document.uri.fsPath;
    const fileDecorations = this.decorations.get(filePath);

    if (!fileDecorations || fileDecorations.length === 0) {
      editor.setDecorations(this.decorationType, []);
      return;
    }

    // Create decoration options with hover messages
    const decorationOptions: vscode.DecorationOptions[] = fileDecorations.map(decoration => {
      const hoverMessage = this.createHoverMessage(decoration);
      
      return {
        range: decoration.range,
        hoverMessage
      };
    });

    editor.setDecorations(this.decorationType, decorationOptions);
  }

  private createHoverMessage(decoration: DecorationInfo): vscode.MarkdownString {
    const group = decoration.duplicateGroup;
    const block = decoration.codeBlock;
    
    const markdown = new vscode.MarkdownString();
    markdown.isTrusted = true;
    
    markdown.appendMarkdown(`**Duplicate Code Detected**\n\n`);
    markdown.appendMarkdown(`- **Similarity**: ${Math.round(group.similarity * 100)}%\n`);
    markdown.appendMarkdown(`- **Lines**: ${group.lineCount}\n`);
    markdown.appendMarkdown(`- **Language**: ${group.language}\n`);
    markdown.appendMarkdown(`- **Total Duplicates**: ${group.blocks.length}\n\n`);
    
    markdown.appendMarkdown(`**Other Locations:**\n`);
    
    for (const otherBlock of group.blocks) {
      if (otherBlock !== block) {
        const relativePath = vscode.workspace.asRelativePath(otherBlock.filePath);
        const command = `command:duplicateFinder.navigateToDuplicate?${encodeURIComponent(JSON.stringify(otherBlock))}`;
        markdown.appendMarkdown(`- [${relativePath}:${otherBlock.startLine}-${otherBlock.endLine}](${command})\n`);
      }
    }
    
    return markdown;
  }

  public dispose(): void {
    this.decorationType.dispose();
    this.decorations.clear();
  }
}
