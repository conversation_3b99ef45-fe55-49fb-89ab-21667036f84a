import * as vscode from 'vscode';
import * as path from 'path';
import { ASTParser } from './astParser';
import { SimilarityAnalyzer } from './similarityAnalyzer';
import { DuplicateGroup, CodeBlock, DuplicateFinderConfig } from './types';

export class DuplicateDetector {
  private astParser: ASTParser;
  private similarityAnalyzer: SimilarityAnalyzer;

  constructor() {
    this.astParser = new ASTParser();
    this.similarityAnalyzer = new SimilarityAnalyzer();
  }

  public async scanWorkspace(): Promise<DuplicateGroup[]> {
    const config = this.getConfig();
    const files = await this.getFilesToScan(config);
    
    console.log(`Scanning ${files.length} files for duplicates...`);
    
    // Parse all files and extract code blocks
    const allBlocks: CodeBlock[] = [];
    
    for (const file of files) {
      try {
        const parsedFile = await this.astParser.parseFile(file);
        if (parsedFile && parsedFile.blocks.length > 0) {
          allBlocks.push(...parsedFile.blocks);
        }
      } catch (error) {
        console.error(`Error parsing file ${file}:`, error);
      }
    }

    console.log(`Extracted ${allBlocks.length} code blocks`);

    // Find duplicate groups
    const duplicateGroups = this.findDuplicateGroups(allBlocks, config);
    
    console.log(`Found ${duplicateGroups.length} duplicate groups`);
    
    return duplicateGroups;
  }

  private async getFilesToScan(config: DuplicateFinderConfig): Promise<string[]> {
    if (!vscode.workspace.workspaceFolders) {
      return [];
    }

    const files: string[] = [];
    const supportedExtensions = ['.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cs', '.cpp', '.c', '.go', '.rs', '.php'];
    
    for (const folder of vscode.workspace.workspaceFolders) {
      const pattern = new vscode.RelativePattern(folder, '**/*');
      const foundFiles = await vscode.workspace.findFiles(pattern);
      
      for (const file of foundFiles) {
        const filePath = file.fsPath;
        const extension = path.extname(filePath).toLowerCase();
        
        // Check if file extension is supported
        if (!supportedExtensions.includes(extension)) {
          continue;
        }
        
        // Check exclude patterns
        if (this.isExcluded(filePath, config.excludePatterns)) {
          continue;
        }
        
        files.push(filePath);
      }
    }

    return files;
  }

  private isExcluded(filePath: string, excludePatterns: string[]): boolean {
    const relativePath = vscode.workspace.asRelativePath(filePath);
    
    return excludePatterns.some(pattern => {
      // Convert glob pattern to regex
      const regexPattern = pattern
        .replace(/\*\*/g, '.*')
        .replace(/\*/g, '[^/]*')
        .replace(/\?/g, '[^/]');
      
      const regex = new RegExp(regexPattern);
      return regex.test(relativePath);
    });
  }

  private findDuplicateGroups(blocks: CodeBlock[], config: DuplicateFinderConfig): DuplicateGroup[] {
    const groups: DuplicateGroup[] = [];
    const processed = new Set<string>();

    for (let i = 0; i < blocks.length; i++) {
      const block1 = blocks[i];
      
      if (processed.has(block1.hash)) {
        continue;
      }

      // Check if block meets minimum line requirement
      const lineCount = block1.endLine - block1.startLine + 1;
      if (lineCount < config.minLines) {
        continue;
      }

      const duplicates: CodeBlock[] = [block1];
      processed.add(block1.hash);

      // Find similar blocks
      for (let j = i + 1; j < blocks.length; j++) {
        const block2 = blocks[j];
        
        if (processed.has(block2.hash)) {
          continue;
        }

        // Skip if blocks are from the same file and too close
        if (block1.filePath === block2.filePath) {
          const gap = Math.abs(block1.startLine - block2.startLine);
          if (gap < config.minLines) {
            continue;
          }
        }

        const similarity = this.similarityAnalyzer.analyzeSimilarity(block1, block2);
        
        if (similarity.isSignificant && similarity.similarity >= config.similarityThreshold) {
          duplicates.push(block2);
          processed.add(block2.hash);
        }
      }

      // Create group if we found duplicates
      if (duplicates.length > 1) {
        const avgSimilarity = this.calculateAverageSimilarity(duplicates);
        
        groups.push({
          id: this.generateGroupId(duplicates),
          blocks: duplicates,
          similarity: avgSimilarity,
          lineCount,
          language: this.detectLanguageFromPath(block1.filePath)
        });
      }
    }

    // Sort groups by severity (line count * number of duplicates * similarity)
    groups.sort((a, b) => {
      const severityA = a.lineCount * a.blocks.length * a.similarity;
      const severityB = b.lineCount * b.blocks.length * b.similarity;
      return severityB - severityA;
    });

    return groups;
  }

  private calculateAverageSimilarity(blocks: CodeBlock[]): number {
    if (blocks.length < 2) {
      return 1.0;
    }

    let totalSimilarity = 0;
    let comparisons = 0;

    for (let i = 0; i < blocks.length; i++) {
      for (let j = i + 1; j < blocks.length; j++) {
        const similarity = this.similarityAnalyzer.analyzeSimilarity(blocks[i], blocks[j]);
        totalSimilarity += similarity.similarity;
        comparisons++;
      }
    }

    return comparisons > 0 ? totalSimilarity / comparisons : 1.0;
  }

  private generateGroupId(blocks: CodeBlock[]): string {
    // Generate a unique ID based on the first block's hash and file count
    const firstBlock = blocks[0];
    const fileCount = new Set(blocks.map(b => b.filePath)).size;
    return `${firstBlock.hash}_${fileCount}_${blocks.length}`;
  }

  private detectLanguageFromPath(filePath: string): string {
    const extension = path.extname(filePath).toLowerCase();
    
    const languageMap: { [key: string]: string } = {
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.py': 'python',
      '.java': 'java',
      '.cs': 'csharp',
      '.cpp': 'cpp',
      '.c': 'c',
      '.go': 'go',
      '.rs': 'rust',
      '.php': 'php'
    };

    return languageMap[extension] || 'unknown';
  }

  private getConfig(): DuplicateFinderConfig {
    const config = vscode.workspace.getConfiguration('duplicateFinder');
    return {
      minLines: config.get('minLines', 5),
      similarityThreshold: config.get('similarityThreshold', 0.8),
      excludePatterns: config.get('excludePatterns', [
        '**/node_modules/**',
        '**/dist/**',
        '**/build/**',
        '**/*.min.js'
      ]),
      enabledLanguages: config.get('enabledLanguages', [
        'javascript',
        'typescript',
        'python',
        'java',
        'csharp',
        'cpp',
        'c',
        'go',
        'rust',
        'php'
      ]),
      autoScan: config.get('autoScan', true)
    };
  }
}
