"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimilarityAnalyzer = void 0;
class SimilarityAnalyzer {
    analyzeSimilarity(block1, block2) {
        // Calculate different types of similarity
        const textualSimilarity = this.calculateTextualSimilarity(block1.content, block2.content);
        const structuralSimilarity = this.calculateStructuralSimilarity(block1, block2);
        // Weighted combination of similarities
        const similarity = (textualSimilarity * 0.4) + (structuralSimilarity * 0.6);
        // Determine if this is significant duplication
        const isSignificant = this.isSignificantDuplication(block1, block2, similarity);
        return {
            similarity,
            structuralSimilarity,
            textualSimilarity,
            isSignificant
        };
    }
    calculateTextualSimilarity(content1, content2) {
        // Normalize content for comparison
        const normalized1 = this.normalizeForComparison(content1);
        const normalized2 = this.normalizeForComparison(content2);
        if (normalized1 === normalized2) {
            return 1.0;
        }
        // Use Levenshtein distance for similarity
        return this.levenshteinSimilarity(normalized1, normalized2);
    }
    calculateStructuralSimilarity(block1, block2) {
        // Extract structural features
        const features1 = this.extractStructuralFeatures(block1.content);
        const features2 = this.extractStructuralFeatures(block2.content);
        // Compare structural features
        return this.compareStructuralFeatures(features1, features2);
    }
    normalizeForComparison(content) {
        return content
            .replace(/\s+/g, ' ') // Normalize whitespace
            .replace(/\/\/.*$/gm, '') // Remove comments
            .replace(/\/\*[\s\S]*?\*\//g, '')
            .replace(/\b\d+\b/g, 'NUM') // Replace numbers with placeholder
            .replace(/["'`][^"'`]*["'`]/g, 'STR') // Replace strings with placeholder
            .replace(/\b[a-zA-Z_]\w*\b/g, (match) => {
            // Keep keywords, replace identifiers
            const keywords = ['if', 'else', 'for', 'while', 'function', 'class', 'return', 'var', 'let', 'const'];
            return keywords.includes(match.toLowerCase()) ? match : 'ID';
        })
            .trim();
    }
    extractStructuralFeatures(content) {
        const lines = content.split('\n');
        return {
            lineCount: lines.length,
            braceCount: (content.match(/{/g) || []).length,
            parenCount: (content.match(/\(/g) || []).length,
            semicolonCount: (content.match(/;/g) || []).length,
            keywordCount: this.countKeywords(content),
            indentationPattern: this.getIndentationPattern(lines),
            controlStructures: this.countControlStructures(content)
        };
    }
    countKeywords(content) {
        const keywords = ['if', 'else', 'for', 'while', 'function', 'class', 'return', 'var', 'let', 'const', 'try', 'catch'];
        let count = 0;
        keywords.forEach(keyword => {
            const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
            const matches = content.match(regex);
            count += matches ? matches.length : 0;
        });
        return count;
    }
    getIndentationPattern(lines) {
        return lines.map(line => {
            const match = line.match(/^(\s*)/);
            return match ? match[1].length : 0;
        });
    }
    countControlStructures(content) {
        const controlKeywords = ['if', 'else', 'for', 'while', 'switch', 'try', 'catch'];
        let count = 0;
        controlKeywords.forEach(keyword => {
            const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
            const matches = content.match(regex);
            count += matches ? matches.length : 0;
        });
        return count;
    }
    compareStructuralFeatures(features1, features2) {
        let similarity = 0;
        let weights = 0;
        // Compare line count (weight: 0.1)
        const lineCountSim = 1 - Math.abs(features1.lineCount - features2.lineCount) / Math.max(features1.lineCount, features2.lineCount);
        similarity += lineCountSim * 0.1;
        weights += 0.1;
        // Compare brace count (weight: 0.2)
        if (features1.braceCount > 0 || features2.braceCount > 0) {
            const braceCountSim = 1 - Math.abs(features1.braceCount - features2.braceCount) / Math.max(features1.braceCount, features2.braceCount, 1);
            similarity += braceCountSim * 0.2;
            weights += 0.2;
        }
        // Compare keyword count (weight: 0.3)
        if (features1.keywordCount > 0 || features2.keywordCount > 0) {
            const keywordCountSim = 1 - Math.abs(features1.keywordCount - features2.keywordCount) / Math.max(features1.keywordCount, features2.keywordCount, 1);
            similarity += keywordCountSim * 0.3;
            weights += 0.3;
        }
        // Compare indentation pattern (weight: 0.2)
        const indentSim = this.compareIndentationPatterns(features1.indentationPattern, features2.indentationPattern);
        similarity += indentSim * 0.2;
        weights += 0.2;
        // Compare control structures (weight: 0.2)
        if (features1.controlStructures > 0 || features2.controlStructures > 0) {
            const controlSim = 1 - Math.abs(features1.controlStructures - features2.controlStructures) / Math.max(features1.controlStructures, features2.controlStructures, 1);
            similarity += controlSim * 0.2;
            weights += 0.2;
        }
        return weights > 0 ? similarity / weights : 0;
    }
    compareIndentationPatterns(pattern1, pattern2) {
        const maxLength = Math.max(pattern1.length, pattern2.length);
        if (maxLength === 0)
            return 1;
        let matches = 0;
        for (let i = 0; i < maxLength; i++) {
            const indent1 = i < pattern1.length ? pattern1[i] : 0;
            const indent2 = i < pattern2.length ? pattern2[i] : 0;
            if (indent1 === indent2)
                matches++;
        }
        return matches / maxLength;
    }
    levenshteinSimilarity(str1, str2) {
        const distance = this.levenshteinDistance(str1, str2);
        const maxLength = Math.max(str1.length, str2.length);
        return maxLength === 0 ? 1 : 1 - (distance / maxLength);
    }
    levenshteinDistance(str1, str2) {
        const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
        for (let i = 0; i <= str1.length; i++) {
            matrix[0][i] = i;
        }
        for (let j = 0; j <= str2.length; j++) {
            matrix[j][0] = j;
        }
        for (let j = 1; j <= str2.length; j++) {
            for (let i = 1; i <= str1.length; i++) {
                const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[j][i] = Math.min(matrix[j][i - 1] + 1, // deletion
                matrix[j - 1][i] + 1, // insertion
                matrix[j - 1][i - 1] + indicator // substitution
                );
            }
        }
        return matrix[str2.length][str1.length];
    }
    isSignificantDuplication(block1, block2, similarity) {
        // Don't flag as duplicate if blocks are in the same file and adjacent
        if (block1.filePath === block2.filePath) {
            const lineGap = Math.abs(block1.startLine - block2.endLine);
            if (lineGap < 5) {
                return false;
            }
        }
        // Check for trivial patterns that shouldn't be flagged
        if (this.isTrivialDuplication(block1.content, block2.content)) {
            return false;
        }
        // Require higher similarity for shorter blocks
        const minLines = Math.min(block1.endLine - block1.startLine, block2.endLine - block2.startLine);
        const requiredSimilarity = minLines < 5 ? 0.95 : minLines < 10 ? 0.85 : 0.75;
        return similarity >= requiredSimilarity;
    }
    isTrivialDuplication(content1, content2) {
        const trivialPatterns = [
            /^const\s+\w+\s*=\s*document\./,
            /^import\s+.*from\s+['"].*['"];?$/,
            /^export\s+.*$/,
            /^\s*{\s*$/,
            /^\s*}\s*$/,
            /^\s*return\s+\w+;\s*$/,
            /^\s*console\.log\(/
        ];
        const lines1 = content1.split('\n').filter(line => line.trim());
        const lines2 = content2.split('\n').filter(line => line.trim());
        // If most lines match trivial patterns, consider it trivial
        const trivialCount1 = lines1.filter(line => trivialPatterns.some(pattern => pattern.test(line.trim()))).length;
        const trivialCount2 = lines2.filter(line => trivialPatterns.some(pattern => pattern.test(line.trim()))).length;
        return (trivialCount1 / lines1.length > 0.7) || (trivialCount2 / lines2.length > 0.7);
    }
}
exports.SimilarityAnalyzer = SimilarityAnalyzer;
//# sourceMappingURL=similarityAnalyzer.js.map