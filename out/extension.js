"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
// Simple tree item class
class DuplicateTreeItem extends vscode.TreeItem {
    constructor(label, collapsibleState, command) {
        super(label, collapsibleState);
        this.label = label;
        this.collapsibleState = collapsibleState;
        this.command = command;
        this.tooltip = this.label;
    }
}
// Simple tree data provider
class DuplicateTreeDataProvider {
    constructor() {
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
        this.items = [];
    }
    getTreeItem(element) {
        return element;
    }
    getChildren(element) {
        if (!element) {
            if (this.items.length === 0) {
                return Promise.resolve([
                    new DuplicateTreeItem('No duplicates found', vscode.TreeItemCollapsibleState.None, {
                        command: 'duplicateFinder.scanWorkspace',
                        title: 'Scan for Duplicates'
                    })
                ]);
            }
            return Promise.resolve(this.items);
        }
        return Promise.resolve([]);
    }
    addItem(label) {
        this.items.push(new DuplicateTreeItem(label, vscode.TreeItemCollapsibleState.None));
        this._onDidChangeTreeData.fire();
    }
    clear() {
        this.items = [];
        this._onDidChangeTreeData.fire();
    }
}
function activate(context) {
    console.log('Duplicate Finder extension is now active!');
    // Simple tree data provider
    const treeDataProvider = new DuplicateTreeDataProvider();
    vscode.window.registerTreeDataProvider('duplicateFinderView', treeDataProvider);
    // Register commands
    const scanCommand = vscode.commands.registerCommand('duplicateFinder.scanWorkspace', async () => {
        vscode.window.showInformationMessage('Scanning for duplicates...');
        // Simple demonstration - add some dummy items
        treeDataProvider.clear();
        treeDataProvider.addItem('Example duplicate 1');
        treeDataProvider.addItem('Example duplicate 2');
        vscode.window.showInformationMessage('Found 2 example duplicates');
    });
    const clearCommand = vscode.commands.registerCommand('duplicateFinder.clearHighlights', () => {
        treeDataProvider.clear();
        vscode.window.showInformationMessage('Cleared duplicate highlights');
    });
    const showPanelCommand = vscode.commands.registerCommand('duplicateFinder.showDuplicatePanel', async () => {
        await vscode.commands.executeCommand('duplicateFinderView.focus');
        vscode.window.showInformationMessage('Duplicate panel focused');
    });
    // Add subscriptions
    context.subscriptions.push(scanCommand, clearCommand, showPanelCommand);
}
exports.activate = activate;
function deactivate() {
    // Clean up resources
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map