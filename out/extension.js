"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
// Define colors for different duplicate groups
const DUPLICATE_COLORS = [
    '#ff000020',
    '#00ff0020',
    '#0000ff20',
    '#ffff0020',
    '#ff00ff20',
    '#00ffff20',
    '#ffa50020',
    '#80008020', // Purple
];
let decorationTypes = [];
let currentEditor;
let duplicateGroups = [];
let duplicateProvider;
let currentNavigationIndex = 0;
let currentGroupIndex = 0;
// Tree data provider for duplicate management
class DuplicateTreeDataProvider {
    constructor() {
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
    }
    getTreeItem(element) {
        return element;
    }
    getChildren(element) {
        if (!element) {
            // Root level - show duplicate groups
            if (duplicateGroups.length === 0) {
                return Promise.resolve([
                    new DuplicateTreeItem('No duplicates found', vscode.TreeItemCollapsibleState.None, 'info')
                ]);
            }
            return Promise.resolve(duplicateGroups.map((group, index) => new DuplicateTreeItem(`${group.content.trim()} (${group.count} instances)`, vscode.TreeItemCollapsibleState.None, 'duplicate', index)));
        }
        return Promise.resolve([]);
    }
    refresh() {
        this._onDidChangeTreeData.fire();
    }
}
class DuplicateTreeItem extends vscode.TreeItem {
    constructor(label, collapsibleState, contextValue, groupIndex) {
        super(label, collapsibleState);
        this.label = label;
        this.collapsibleState = collapsibleState;
        this.contextValue = contextValue;
        this.groupIndex = groupIndex;
        if (contextValue === 'duplicate' && groupIndex !== undefined) {
            this.command = {
                command: 'duplicateFinder.navigateToGroup',
                title: 'Navigate to Duplicate',
                arguments: [groupIndex]
            };
            this.iconPath = new vscode.ThemeIcon('warning');
        }
        else if (contextValue === 'info') {
            this.iconPath = new vscode.ThemeIcon('info');
        }
    }
}
function activate(context) {
    console.log('Duplicate Finder extension is now active!');
    // Initialize components
    initializeDecorationTypes();
    duplicateProvider = new DuplicateTreeDataProvider();
    // Register tree data provider
    vscode.window.registerTreeDataProvider('duplicateFinderView', duplicateProvider);
    // Set context to show the view
    vscode.commands.executeCommand('setContext', 'duplicateFinder.active', true);
    // Register commands
    const scanCommand = vscode.commands.registerCommand('duplicateFinder.scanDocument', () => {
        scanCurrentDocument();
    });
    const clearCommand = vscode.commands.registerCommand('duplicateFinder.clearHighlights', () => {
        clearHighlights();
        duplicateGroups = [];
        duplicateProvider.refresh();
    });
    const navigateToGroupCommand = vscode.commands.registerCommand('duplicateFinder.navigateToGroup', (groupIndex) => {
        navigateToGroup(groupIndex);
    });
    const nextInstanceCommand = vscode.commands.registerCommand('duplicateFinder.nextInstance', () => {
        navigateToNextInstance();
    });
    const prevInstanceCommand = vscode.commands.registerCommand('duplicateFinder.prevInstance', () => {
        navigateToPrevInstance();
    });
    const removeLineCommand = vscode.commands.registerCommand('duplicateFinder.removeLine', () => {
        removeCurrentLine();
    });
    const keepLineCommand = vscode.commands.registerCommand('duplicateFinder.keepLine', () => {
        keepCurrentLine();
    });
    // Listen for active editor changes
    vscode.window.onDidChangeActiveTextEditor(editor => {
        currentEditor = editor;
        if (editor) {
            scanCurrentDocument();
        }
    });
    // Listen for document changes
    vscode.workspace.onDidChangeTextDocument(event => {
        if (currentEditor && event.document === currentEditor.document) {
            // Debounce the scanning to avoid too frequent updates
            setTimeout(() => scanCurrentDocument(), 500);
        }
    });
    // Set initial editor
    currentEditor = vscode.window.activeTextEditor;
    if (currentEditor) {
        scanCurrentDocument();
    }
    context.subscriptions.push(scanCommand, clearCommand, navigateToGroupCommand, nextInstanceCommand, prevInstanceCommand, removeLineCommand, keepLineCommand);
}
exports.activate = activate;
function initializeDecorationTypes() {
    // Create decoration types for each color
    decorationTypes = DUPLICATE_COLORS.map(color => vscode.window.createTextEditorDecorationType({
        backgroundColor: color,
        isWholeLine: true,
        overviewRulerColor: color.replace('20', '80'),
        overviewRulerLane: vscode.OverviewRulerLane.Right
    }));
}
function scanCurrentDocument() {
    if (!currentEditor) {
        return;
    }
    const document = currentEditor.document;
    const text = document.getText();
    const lines = text.split('\n');
    // Clear previous highlights
    clearHighlights();
    // Find duplicate lines
    duplicateGroups = findDuplicateLines(lines);
    // Apply highlights
    highlightDuplicateGroups(duplicateGroups);
    // Update tree view
    duplicateProvider.refresh();
    // Set context for view visibility
    vscode.commands.executeCommand('setContext', 'duplicateFinder.hasResults', duplicateGroups.length > 0);
    // Show status message
    const totalDuplicates = duplicateGroups.reduce((sum, group) => sum + group.lineNumbers.length, 0);
    if (duplicateGroups.length > 0) {
        vscode.window.showInformationMessage(`Found ${duplicateGroups.length} groups of duplicate lines (${totalDuplicates} total lines)`);
    }
}
function findDuplicateLines(lines) {
    const lineMap = new Map();
    // Group lines by content (ignoring empty lines and whitespace-only lines)
    lines.forEach((line, index) => {
        const trimmedLine = line.trim();
        if (trimmedLine.length > 0) {
            if (!lineMap.has(trimmedLine)) {
                lineMap.set(trimmedLine, []);
            }
            lineMap.get(trimmedLine).push(index);
        }
    });
    // Filter to only include lines that appear more than once
    const duplicateGroups = [];
    let colorIndex = 0;
    lineMap.forEach((lineNumbers, content) => {
        if (lineNumbers.length > 1) {
            duplicateGroups.push({
                content,
                lineNumbers,
                count: lineNumbers.length,
                colorIndex: colorIndex % DUPLICATE_COLORS.length
            });
            colorIndex++;
        }
    });
    // Sort by count (most duplicates first)
    duplicateGroups.sort((a, b) => b.count - a.count);
    return duplicateGroups;
}
function highlightDuplicateGroups(groups) {
    if (!currentEditor) {
        return;
    }
    groups.forEach((group) => {
        const decorationType = decorationTypes[group.colorIndex];
        const ranges = group.lineNumbers.map(lineNumber => new vscode.Range(lineNumber, 0, lineNumber, Number.MAX_SAFE_INTEGER));
        currentEditor.setDecorations(decorationType, ranges);
    });
}
function clearHighlights() {
    if (!currentEditor) {
        return;
    }
    decorationTypes.forEach(decorationType => {
        currentEditor.setDecorations(decorationType, []);
    });
}
function navigateToGroup(groupIndex) {
    if (!currentEditor || groupIndex >= duplicateGroups.length) {
        return;
    }
    currentGroupIndex = groupIndex;
    currentNavigationIndex = 0;
    const group = duplicateGroups[groupIndex];
    if (group.lineNumbers.length > 0) {
        navigateToLine(group.lineNumbers[0]);
        showNavigationButtons();
    }
}
function navigateToNextInstance() {
    if (!currentEditor || currentGroupIndex >= duplicateGroups.length) {
        return;
    }
    const group = duplicateGroups[currentGroupIndex];
    currentNavigationIndex = (currentNavigationIndex + 1) % group.lineNumbers.length;
    navigateToLine(group.lineNumbers[currentNavigationIndex]);
    showNavigationButtons();
}
function navigateToPrevInstance() {
    if (!currentEditor || currentGroupIndex >= duplicateGroups.length) {
        return;
    }
    const group = duplicateGroups[currentGroupIndex];
    currentNavigationIndex = currentNavigationIndex === 0
        ? group.lineNumbers.length - 1
        : currentNavigationIndex - 1;
    navigateToLine(group.lineNumbers[currentNavigationIndex]);
    showNavigationButtons();
}
function navigateToLine(lineNumber) {
    if (!currentEditor) {
        return;
    }
    const position = new vscode.Position(lineNumber, 0);
    const range = new vscode.Range(position, position);
    currentEditor.selection = new vscode.Selection(position, position);
    currentEditor.revealRange(range, vscode.TextEditorRevealType.InCenter);
}
function showNavigationButtons() {
    if (!currentEditor || currentGroupIndex >= duplicateGroups.length) {
        return;
    }
    const group = duplicateGroups[currentGroupIndex];
    const message = `Duplicate ${currentNavigationIndex + 1} of ${group.lineNumbers.length}: "${group.content.trim()}"`;
    vscode.window.showInformationMessage(message, 'Previous', 'Next', 'Remove Line', 'Keep Line').then(selection => {
        switch (selection) {
            case 'Previous':
                navigateToPrevInstance();
                break;
            case 'Next':
                navigateToNextInstance();
                break;
            case 'Remove Line':
                removeCurrentLine();
                break;
            case 'Keep Line':
                keepCurrentLine();
                break;
        }
    });
}
function removeCurrentLine() {
    if (!currentEditor || currentGroupIndex >= duplicateGroups.length) {
        return;
    }
    const group = duplicateGroups[currentGroupIndex];
    const lineNumber = group.lineNumbers[currentNavigationIndex];
    // Remove the line from the document
    const edit = new vscode.WorkspaceEdit();
    const lineRange = new vscode.Range(lineNumber, 0, lineNumber + 1, 0);
    edit.delete(currentEditor.document.uri, lineRange);
    vscode.workspace.applyEdit(edit).then(() => {
        // Rescan after removal
        setTimeout(() => scanCurrentDocument(), 100);
    });
}
function keepCurrentLine() {
    if (!currentEditor || currentGroupIndex >= duplicateGroups.length) {
        return;
    }
    const group = duplicateGroups[currentGroupIndex];
    const lineNumber = group.lineNumbers[currentNavigationIndex];
    // Remove this line from the duplicate group
    group.lineNumbers.splice(currentNavigationIndex, 1);
    group.count = group.lineNumbers.length;
    // If only one instance left, remove the entire group
    if (group.lineNumbers.length <= 1) {
        duplicateGroups.splice(currentGroupIndex, 1);
        if (duplicateGroups.length === 0) {
            clearHighlights();
        }
        else {
            // Navigate to next group if available
            if (currentGroupIndex >= duplicateGroups.length) {
                currentGroupIndex = 0;
            }
            navigateToGroup(currentGroupIndex);
        }
    }
    else {
        // Adjust navigation index if needed
        if (currentNavigationIndex >= group.lineNumbers.length) {
            currentNavigationIndex = 0;
        }
        navigateToLine(group.lineNumbers[currentNavigationIndex]);
        showNavigationButtons();
    }
    // Update highlights and tree view
    clearHighlights();
    highlightDuplicateGroups(duplicateGroups);
    duplicateProvider.refresh();
}
function deactivate() {
    clearHighlights();
    decorationTypes.forEach(decorationType => decorationType.dispose());
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map