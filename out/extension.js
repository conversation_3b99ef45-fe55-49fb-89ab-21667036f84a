"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const duplicateDetector_1 = require("./duplicateDetector");
const duplicateProvider_1 = require("./duplicateProvider");
const decorationManager_1 = require("./decorationManager");
let duplicateDetector;
let duplicateProvider;
let decorationManager;
let statusBarItem;
function activate(context) {
    console.log('Duplicate Finder extension is now active!');
    // Initialize components
    duplicateDetector = new duplicateDetector_1.DuplicateDetector();
    duplicateProvider = new duplicateProvider_1.DuplicateProvider();
    decorationManager = new decorationManager_1.DecorationManager();
    // Create status bar item
    statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
    statusBarItem.command = 'duplicateFinder.showDuplicatePanel';
    context.subscriptions.push(statusBarItem);
    // Register tree data provider
    vscode.window.registerTreeDataProvider('duplicateFinderView', duplicateProvider);
    // Register commands
    const scanCommand = vscode.commands.registerCommand('duplicateFinder.scanWorkspace', async () => {
        await scanWorkspace();
    });
    const clearCommand = vscode.commands.registerCommand('duplicateFinder.clearHighlights', () => {
        decorationManager.clearAllDecorations();
        duplicateProvider.clear();
        updateStatusBar(0);
    });
    const showPanelCommand = vscode.commands.registerCommand('duplicateFinder.showDuplicatePanel', async () => {
        // Focus the view
        await vscode.commands.executeCommand('duplicateFinderView.focus');
        // If no duplicates, show a message
        if (duplicateProvider.isEmpty()) {
            vscode.window.showInformationMessage('No duplicates found. Run "Scan for Duplicates" first.');
        }
    });
    const navigateCommand = vscode.commands.registerCommand('duplicateFinder.navigateToDuplicate', (codeBlock) => {
        navigateToCodeBlock(codeBlock);
    });
    // Register file system watcher
    const watcher = vscode.workspace.createFileSystemWatcher('**/*');
    watcher.onDidChange(async (uri) => {
        const config = getConfig();
        if (config.autoScan && isFileSupported(uri.fsPath)) {
            await scanWorkspace();
        }
    });
    watcher.onDidCreate(async (uri) => {
        const config = getConfig();
        if (config.autoScan && isFileSupported(uri.fsPath)) {
            await scanWorkspace();
        }
    });
    watcher.onDidDelete(async () => {
        const config = getConfig();
        if (config.autoScan) {
            await scanWorkspace();
        }
    });
    // Add subscriptions
    context.subscriptions.push(scanCommand, clearCommand, showPanelCommand, navigateCommand, watcher);
    // Initial scan if auto-scan is enabled
    const config = getConfig();
    if (config.autoScan) {
        setTimeout(() => scanWorkspace(), 2000); // Delay to let workspace settle
    }
}
exports.activate = activate;
async function scanWorkspace() {
    if (!vscode.workspace.workspaceFolders) {
        vscode.window.showWarningMessage('No workspace folder is open');
        return;
    }
    statusBarItem.text = '$(sync~spin) Scanning for duplicates...';
    statusBarItem.show();
    try {
        const duplicateGroups = await duplicateDetector.scanWorkspace();
        // Update tree view
        duplicateProvider.updateDuplicates(duplicateGroups);
        // Update decorations
        decorationManager.updateDecorations(duplicateGroups);
        // Update status bar
        const totalDuplicates = duplicateGroups.reduce((sum, group) => sum + group.blocks.length, 0);
        updateStatusBar(totalDuplicates);
        if (duplicateGroups.length > 0) {
            vscode.window.showInformationMessage(`Found ${duplicateGroups.length} duplicate groups with ${totalDuplicates} total duplicates`);
        }
        else {
            vscode.window.showInformationMessage('No significant duplicates found');
        }
    }
    catch (error) {
        console.error('Error scanning workspace:', error);
        vscode.window.showErrorMessage(`Error scanning for duplicates: ${error}`);
        statusBarItem.hide();
    }
}
function updateStatusBar(duplicateCount) {
    if (duplicateCount > 0) {
        statusBarItem.text = `$(warning) ${duplicateCount} duplicates`;
        statusBarItem.show();
    }
    else {
        statusBarItem.hide();
    }
}
async function navigateToCodeBlock(codeBlock) {
    try {
        const document = await vscode.workspace.openTextDocument(codeBlock.filePath);
        const editor = await vscode.window.showTextDocument(document);
        const startPos = new vscode.Position(codeBlock.startLine, 0);
        const endPos = new vscode.Position(codeBlock.endLine, 0);
        const range = new vscode.Range(startPos, endPos);
        editor.selection = new vscode.Selection(startPos, endPos);
        editor.revealRange(range, vscode.TextEditorRevealType.InCenter);
    }
    catch (error) {
        vscode.window.showErrorMessage(`Could not navigate to duplicate: ${error}`);
    }
}
function getConfig() {
    const config = vscode.workspace.getConfiguration('duplicateFinder');
    return {
        minLines: config.get('minLines', 5),
        similarityThreshold: config.get('similarityThreshold', 0.8),
        excludePatterns: config.get('excludePatterns', []),
        enabledLanguages: config.get('enabledLanguages', []),
        autoScan: config.get('autoScan', true)
    };
}
function isFileSupported(filePath) {
    const extension = filePath.split('.').pop()?.toLowerCase();
    // Check if file extension is supported
    const supportedExtensions = ['js', 'ts', 'py', 'java', 'cs', 'cpp', 'c', 'go', 'rs', 'php'];
    return extension ? supportedExtensions.includes(extension) : false;
}
function deactivate() {
    if (statusBarItem) {
        statusBarItem.dispose();
    }
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map