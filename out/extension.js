"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
// Define colors for different duplicate groups
const DUPLICATE_COLORS = [
    '#ff000020',
    '#00ff0020',
    '#0000ff20',
    '#ffff0020',
    '#ff00ff20',
    '#00ffff20',
    '#ffa50020',
    '#80008020', // Purple
];
let decorationTypes = [];
let currentEditor;
function activate(context) {
    console.log('Duplicate Finder extension is now active!');
    // Initialize decoration types
    initializeDecorationTypes();
    // Register commands
    const scanCommand = vscode.commands.registerCommand('duplicateFinder.scanDocument', () => {
        scanCurrentDocument();
    });
    const clearCommand = vscode.commands.registerCommand('duplicateFinder.clearHighlights', () => {
        clearHighlights();
    });
    // Listen for active editor changes
    vscode.window.onDidChangeActiveTextEditor(editor => {
        currentEditor = editor;
        if (editor) {
            scanCurrentDocument();
        }
    });
    // Listen for document changes
    vscode.workspace.onDidChangeTextDocument(event => {
        if (currentEditor && event.document === currentEditor.document) {
            // Debounce the scanning to avoid too frequent updates
            setTimeout(() => scanCurrentDocument(), 500);
        }
    });
    // Set initial editor
    currentEditor = vscode.window.activeTextEditor;
    if (currentEditor) {
        scanCurrentDocument();
    }
    context.subscriptions.push(scanCommand, clearCommand);
}
exports.activate = activate;
function initializeDecorationTypes() {
    // Create decoration types for each color
    decorationTypes = DUPLICATE_COLORS.map(color => vscode.window.createTextEditorDecorationType({
        backgroundColor: color,
        isWholeLine: true,
        overviewRulerColor: color.replace('20', '80'),
        overviewRulerLane: vscode.OverviewRulerLane.Right
    }));
}
function scanCurrentDocument() {
    if (!currentEditor) {
        return;
    }
    const document = currentEditor.document;
    const text = document.getText();
    const lines = text.split('\n');
    // Clear previous highlights
    clearHighlights();
    // Find duplicate lines
    const duplicateGroups = findDuplicateLines(lines);
    // Apply highlights
    highlightDuplicateGroups(duplicateGroups);
    // Show status message
    const totalDuplicates = duplicateGroups.reduce((sum, group) => sum + group.lineNumbers.length, 0);
    if (duplicateGroups.length > 0) {
        vscode.window.showInformationMessage(`Found ${duplicateGroups.length} groups of duplicate lines (${totalDuplicates} total lines)`);
    }
}
function findDuplicateLines(lines) {
    const lineMap = new Map();
    // Group lines by content (ignoring empty lines and whitespace-only lines)
    lines.forEach((line, index) => {
        const trimmedLine = line.trim();
        if (trimmedLine.length > 0) {
            if (!lineMap.has(trimmedLine)) {
                lineMap.set(trimmedLine, []);
            }
            lineMap.get(trimmedLine).push(index);
        }
    });
    // Filter to only include lines that appear more than once
    const duplicateGroups = [];
    lineMap.forEach((lineNumbers, content) => {
        if (lineNumbers.length > 1) {
            duplicateGroups.push({
                content,
                lineNumbers,
                count: lineNumbers.length
            });
        }
    });
    // Sort by count (most duplicates first)
    duplicateGroups.sort((a, b) => b.count - a.count);
    return duplicateGroups;
}
function highlightDuplicateGroups(groups) {
    if (!currentEditor) {
        return;
    }
    groups.forEach((group, groupIndex) => {
        const colorIndex = groupIndex % DUPLICATE_COLORS.length;
        const decorationType = decorationTypes[colorIndex];
        const ranges = group.lineNumbers.map(lineNumber => new vscode.Range(lineNumber, 0, lineNumber, Number.MAX_SAFE_INTEGER));
        currentEditor.setDecorations(decorationType, ranges);
    });
}
function clearHighlights() {
    if (!currentEditor) {
        return;
    }
    decorationTypes.forEach(decorationType => {
        currentEditor.setDecorations(decorationType, []);
    });
}
function deactivate() {
    clearHighlights();
    decorationTypes.forEach(decorationType => decorationType.dispose());
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map