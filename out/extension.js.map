{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2DAAwD;AACxD,2DAAwD;AACxD,2DAAwD;AAGxD,IAAI,iBAAoC,CAAC;AACzC,IAAI,iBAAoC,CAAC;AACzC,IAAI,iBAAoC,CAAC;AACzC,IAAI,aAAmC,CAAC;AAExC,SAAgB,QAAQ,CAAC,OAAgC;IACvD,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IAEzD,wBAAwB;IACxB,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAC;IAC5C,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAC;IAC5C,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAC;IAE5C,yBAAyB;IACzB,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACvF,aAAa,CAAC,OAAO,GAAG,oCAAoC,CAAC;IAC7D,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAE1C,8BAA8B;IAC9B,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,qBAAqB,EAAE,iBAAiB,CAAC,CAAC;IAEjF,oBAAoB;IACpB,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QAC9F,MAAM,aAAa,EAAE,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC3F,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;QACxC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC1B,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;QACxG,iBAAiB;QACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,2BAA2B,CAAC,CAAC;QAElE,mCAAmC;QACnC,IAAI,iBAAiB,CAAC,OAAO,EAAE,EAAE;YAC/B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uDAAuD,CAAC,CAAC;SAC/F;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qCAAqC,EAC3F,CAAC,SAAoB,EAAE,EAAE;QACvB,mBAAmB,CAAC,SAAS,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC;IAEL,+BAA+B;IAC/B,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;IACjE,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QAChC,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAC3B,IAAI,MAAM,CAAC,QAAQ,IAAI,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAClD,MAAM,aAAa,EAAE,CAAC;SACvB;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QAChC,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAC3B,IAAI,MAAM,CAAC,QAAQ,IAAI,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAClD,MAAM,aAAa,EAAE,CAAC;SACvB;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;QAC7B,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAC3B,IAAI,MAAM,CAAC,QAAQ,EAAE;YACnB,MAAM,aAAa,EAAE,CAAC;SACvB;IACH,CAAC,CAAC,CAAC;IAEH,oBAAoB;IACpB,OAAO,CAAC,aAAa,CAAC,IAAI,CACxB,WAAW,EACX,YAAY,EACZ,gBAAgB,EAChB,eAAe,EACf,OAAO,CACR,CAAC;IAEF,uCAAuC;IACvC,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;IAC3B,IAAI,MAAM,CAAC,QAAQ,EAAE;QACnB,UAAU,CAAC,GAAG,EAAE,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,gCAAgC;KAC1E;AACH,CAAC;AA/ED,4BA+EC;AAED,KAAK,UAAU,aAAa;IAC1B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE;QACtC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,6BAA6B,CAAC,CAAC;QAChE,OAAO;KACR;IAED,aAAa,CAAC,IAAI,GAAG,yCAAyC,CAAC;IAC/D,aAAa,CAAC,IAAI,EAAE,CAAC;IAErB,IAAI;QACF,MAAM,eAAe,GAAG,MAAM,iBAAiB,CAAC,aAAa,EAAE,CAAC;QAEhE,mBAAmB;QACnB,iBAAiB,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;QAEpD,qBAAqB;QACrB,iBAAiB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;QAErD,oBAAoB;QACpB,MAAM,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACrG,eAAe,CAAC,eAAe,CAAC,CAAC;QAEjC,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,SAAS,eAAe,CAAC,MAAM,0BAA0B,eAAe,mBAAmB,CAC5F,CAAC;SACH;aAAM;YACL,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,iCAAiC,CAAC,CAAC;SACzE;KACF;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAClD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;QAC1E,aAAa,CAAC,IAAI,EAAE,CAAC;KACtB;AACH,CAAC;AAED,SAAS,eAAe,CAAC,cAAsB;IAC7C,IAAI,cAAc,GAAG,CAAC,EAAE;QACtB,aAAa,CAAC,IAAI,GAAG,cAAc,cAAc,aAAa,CAAC;QAC/D,aAAa,CAAC,IAAI,EAAE,CAAC;KACtB;SAAM;QACL,aAAa,CAAC,IAAI,EAAE,CAAC;KACtB;AACH,CAAC;AAED,KAAK,UAAU,mBAAmB,CAAC,SAAoB;IACrD,IAAI;QACF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC7E,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAE9D,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QAC7D,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QACzD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEjD,MAAM,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC1D,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;KACjE;IAAC,OAAO,KAAK,EAAE;QACd,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,oCAAoC,KAAK,EAAE,CAAC,CAAC;KAC7E;AACH,CAAC;AAED,SAAS,SAAS;IAChB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;IACpE,OAAO;QACL,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC;QACnC,mBAAmB,EAAE,MAAM,CAAC,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC;QAC3D,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,EAAE,CAAC;QAClD,gBAAgB,EAAE,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,EAAE,CAAC;QACpD,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC;KACvC,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,QAAgB;IACvC,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,CAAC;IAE3D,uCAAuC;IACvC,MAAM,mBAAmB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAC5F,OAAO,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AACrE,CAAC;AAED,SAAgB,UAAU;IACxB,IAAI,aAAa,EAAE;QACjB,aAAa,CAAC,OAAO,EAAE,CAAC;KACzB;AACH,CAAC;AAJD,gCAIC"}