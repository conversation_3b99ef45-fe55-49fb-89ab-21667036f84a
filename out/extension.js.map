{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,yBAAyB;AACzB,MAAM,iBAAkB,SAAQ,MAAM,CAAC,QAAQ;IAC7C,YACkB,KAAa,EACb,gBAAiD,EACjD,OAAwB;QAExC,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QAJf,UAAK,GAAL,KAAK,CAAQ;QACb,qBAAgB,GAAhB,gBAAgB,CAAiC;QACjD,YAAO,GAAP,OAAO,CAAiB;QAGxC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;IAC5B,CAAC;CACF;AAED,4BAA4B;AAC5B,MAAM,yBAAyB;IAA/B;QACU,yBAAoB,GAAqE,IAAI,MAAM,CAAC,YAAY,EAA+C,CAAC;QAC/J,wBAAmB,GAA8D,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAElH,UAAK,GAAwB,EAAE,CAAC;IAkC1C,CAAC;IAhCC,WAAW,CAAC,OAA0B;QACpC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,WAAW,CAAC,OAA2B;QACrC,IAAI,CAAC,OAAO,EAAE;YACZ,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC3B,OAAO,OAAO,CAAC,OAAO,CAAC;oBACrB,IAAI,iBAAiB,CACnB,qBAAqB,EACrB,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC;wBACE,OAAO,EAAE,+BAA+B;wBACxC,KAAK,EAAE,qBAAqB;qBAC7B,CACF;iBACF,CAAC,CAAC;aACJ;YACD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACpC;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC;IAED,OAAO,CAAC,KAAa;QACnB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;QACpF,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAED,KAAK;QACH,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;CACF;AAED,SAAgB,QAAQ,CAAC,OAAgC;IACvD,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IAEzD,4BAA4B;IAC5B,MAAM,gBAAgB,GAAG,IAAI,yBAAyB,EAAE,CAAC;IACzD,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,qBAAqB,EAAE,gBAAgB,CAAC,CAAC;IAEhF,oBAAoB;IACpB,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QAC9F,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,4BAA4B,CAAC,CAAC;QAEnE,8CAA8C;QAC9C,gBAAgB,CAAC,KAAK,EAAE,CAAC;QACzB,gBAAgB,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;QAChD,gBAAgB,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;QAEhD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,4BAA4B,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC3F,gBAAgB,CAAC,KAAK,EAAE,CAAC;QACzB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,8BAA8B,CAAC,CAAC;IACvE,CAAC,CAAC,CAAC;IAEH,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;QACxG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,2BAA2B,CAAC,CAAC;QAClE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;IAEH,oBAAoB;IACpB,OAAO,CAAC,aAAa,CAAC,IAAI,CACxB,WAAW,EACX,YAAY,EACZ,gBAAgB,CACjB,CAAC;AACJ,CAAC;AAnCD,4BAmCC;AAED,SAAgB,UAAU;IACxB,qBAAqB;AACvB,CAAC;AAFD,gCAEC"}