{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,+CAA+C;AAC/C,MAAM,gBAAgB,GAAG;IACvB,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW,EAAE,SAAS;CACvB,CAAC;AAEF,IAAI,eAAe,GAAsC,EAAE,CAAC;AAC5D,IAAI,aAA4C,CAAC;AAEjD,SAAgB,QAAQ,CAAC,OAAgC;IACvD,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IAEzD,8BAA8B;IAC9B,yBAAyB,EAAE,CAAC;IAE5B,oBAAoB;IACpB,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,GAAG,EAAE;QACvF,mBAAmB,EAAE,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC3F,eAAe,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,mCAAmC;IACnC,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,MAAM,CAAC,EAAE;QACjD,aAAa,GAAG,MAAM,CAAC;QACvB,IAAI,MAAM,EAAE;YACV,mBAAmB,EAAE,CAAC;SACvB;IACH,CAAC,CAAC,CAAC;IAEH,8BAA8B;IAC9B,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;QAC/C,IAAI,aAAa,IAAI,KAAK,CAAC,QAAQ,KAAK,aAAa,CAAC,QAAQ,EAAE;YAC9D,sDAAsD;YACtD,UAAU,CAAC,GAAG,EAAE,CAAC,mBAAmB,EAAE,EAAE,GAAG,CAAC,CAAC;SAC9C;IACH,CAAC,CAAC,CAAC;IAEH,qBAAqB;IACrB,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;IAC/C,IAAI,aAAa,EAAE;QACjB,mBAAmB,EAAE,CAAC;KACvB;IAED,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AACxD,CAAC;AAtCD,4BAsCC;AAED,SAAS,yBAAyB;IAChC,yCAAyC;IACzC,eAAe,GAAG,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAC7C,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC;QAC3C,eAAe,EAAE,KAAK;QACtB,WAAW,EAAE,IAAI;QACjB,kBAAkB,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;QAC7C,iBAAiB,EAAE,MAAM,CAAC,iBAAiB,CAAC,KAAK;KAClD,CAAC,CACH,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB;IAC1B,IAAI,CAAC,aAAa,EAAE;QAClB,OAAO;KACR;IAED,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;IACxC,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;IAChC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAE/B,4BAA4B;IAC5B,eAAe,EAAE,CAAC;IAElB,uBAAuB;IACvB,MAAM,eAAe,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAElD,mBAAmB;IACnB,wBAAwB,CAAC,eAAe,CAAC,CAAC;IAE1C,sBAAsB;IACtB,MAAM,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAClG,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;QAC9B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,SAAS,eAAe,CAAC,MAAM,+BAA+B,eAAe,eAAe,CAC7F,CAAC;KACH;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAe;IACzC,MAAM,OAAO,GAAG,IAAI,GAAG,EAAoB,CAAC;IAE5C,0EAA0E;IAC1E,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;QAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAChC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;gBAC7B,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;aAC9B;YACD,OAAO,CAAC,GAAG,CAAC,WAAW,CAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACvC;IACH,CAAC,CAAC,CAAC;IAEH,0DAA0D;IAC1D,MAAM,eAAe,GAAqB,EAAE,CAAC;IAC7C,OAAO,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,OAAO,EAAE,EAAE;QACvC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,eAAe,CAAC,IAAI,CAAC;gBACnB,OAAO;gBACP,WAAW;gBACX,KAAK,EAAE,WAAW,CAAC,MAAM;aAC1B,CAAC,CAAC;SACJ;IACH,CAAC,CAAC,CAAC;IAEH,wCAAwC;IACxC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IAElD,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,SAAS,wBAAwB,CAAC,MAAwB;IACxD,IAAI,CAAC,aAAa,EAAE;QAClB,OAAO;KACR;IAED,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;QACnC,MAAM,UAAU,GAAG,UAAU,GAAG,gBAAgB,CAAC,MAAM,CAAC;QACxD,MAAM,cAAc,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;QAEnD,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAChD,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE,UAAU,EAAE,MAAM,CAAC,gBAAgB,CAAC,CACrE,CAAC;QAEF,aAAc,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,eAAe;IACtB,IAAI,CAAC,aAAa,EAAE;QAClB,OAAO;KACR;IAED,eAAe,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;QACvC,aAAc,CAAC,cAAc,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;AACL,CAAC;AAQD,SAAgB,UAAU;IACxB,eAAe,EAAE,CAAC;IAClB,eAAe,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC;AACtE,CAAC;AAHD,gCAGC"}