{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,+CAA+C;AAC/C,MAAM,gBAAgB,GAAG;IACvB,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW,EAAE,SAAS;CACvB,CAAC;AAWF,IAAI,eAAe,GAAsC,EAAE,CAAC;AAC5D,IAAI,aAA4C,CAAC;AACjD,IAAI,eAAe,GAAqB,EAAE,CAAC;AAC3C,IAAI,iBAA4C,CAAC;AACjD,IAAI,sBAAsB,GAAG,CAAC,CAAC;AAC/B,IAAI,iBAAiB,GAAG,CAAC,CAAC;AAE1B,8CAA8C;AAC9C,MAAM,yBAAyB;IAA/B;QACU,yBAAoB,GAAqE,IAAI,MAAM,CAAC,YAAY,EAA+C,CAAC;QAC/J,wBAAmB,GAA8D,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAkC5H,CAAC;IAhCC,WAAW,CAAC,OAA0B;QACpC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,WAAW,CAAC,OAA2B;QACrC,IAAI,CAAC,OAAO,EAAE;YACZ,qCAAqC;YACrC,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;gBAChC,OAAO,OAAO,CAAC,OAAO,CAAC;oBACrB,IAAI,iBAAiB,CACnB,qBAAqB,EACrB,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,MAAM,CACP;iBACF,CAAC,CAAC;aACJ;YAED,OAAO,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAC1D,IAAI,iBAAiB,CACnB,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,KAAK,aAAa,EACpD,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,WAAW,EACX,KAAK,CACN,CACF,CAAC,CAAC;SACJ;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC;IAED,OAAO;QACL,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;CACF;AAED,MAAM,iBAAkB,SAAQ,MAAM,CAAC,QAAQ;IAC7C,YACkB,KAAa,EACb,gBAAiD,EACjD,YAAoB,EACpB,UAAmB;QAEnC,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QALf,UAAK,GAAL,KAAK,CAAQ;QACb,qBAAgB,GAAhB,gBAAgB,CAAiC;QACjD,iBAAY,GAAZ,YAAY,CAAQ;QACpB,eAAU,GAAV,UAAU,CAAS;QAInC,IAAI,YAAY,KAAK,WAAW,IAAI,UAAU,KAAK,SAAS,EAAE;YAC5D,IAAI,CAAC,OAAO,GAAG;gBACb,OAAO,EAAE,iCAAiC;gBAC1C,KAAK,EAAE,uBAAuB;gBAC9B,SAAS,EAAE,CAAC,UAAU,CAAC;aACxB,CAAC;YACF,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;SACjD;aAAM,IAAI,YAAY,KAAK,MAAM,EAAE;YAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;SAC9C;IACH,CAAC;CACF;AAED,SAAgB,QAAQ,CAAC,OAAgC;IACvD,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IAEzD,wBAAwB;IACxB,yBAAyB,EAAE,CAAC;IAC5B,iBAAiB,GAAG,IAAI,yBAAyB,EAAE,CAAC;IAEpD,8BAA8B;IAC9B,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,qBAAqB,EAAE,iBAAiB,CAAC,CAAC;IAEjF,+BAA+B;IAC/B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,wBAAwB,EAAE,IAAI,CAAC,CAAC;IAE7E,oBAAoB;IACpB,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,GAAG,EAAE;QACvF,mBAAmB,EAAE,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC3F,eAAe,EAAE,CAAC;QAClB,eAAe,GAAG,EAAE,CAAC;QACrB,iBAAiB,CAAC,OAAO,EAAE,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,MAAM,sBAAsB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iCAAiC,EAAE,CAAC,UAAkB,EAAE,EAAE;QACvH,eAAe,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC/F,sBAAsB,EAAE,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC/F,sBAAsB,EAAE,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC3F,iBAAiB,EAAE,CAAC;IACtB,CAAC,CAAC,CAAC;IAEH,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACvF,eAAe,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,mCAAmC;IACnC,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,MAAM,CAAC,EAAE;QACjD,aAAa,GAAG,MAAM,CAAC;QACvB,IAAI,MAAM,EAAE;YACV,mBAAmB,EAAE,CAAC;SACvB;IACH,CAAC,CAAC,CAAC;IAEH,8BAA8B;IAC9B,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;QAC/C,IAAI,aAAa,IAAI,KAAK,CAAC,QAAQ,KAAK,aAAa,CAAC,QAAQ,EAAE;YAC9D,sDAAsD;YACtD,UAAU,CAAC,GAAG,EAAE,CAAC,mBAAmB,EAAE,EAAE,GAAG,CAAC,CAAC;SAC9C;IACH,CAAC,CAAC,CAAC;IAEH,qBAAqB;IACrB,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;IAC/C,IAAI,aAAa,EAAE;QACjB,mBAAmB,EAAE,CAAC;KACvB;IAED,OAAO,CAAC,aAAa,CAAC,IAAI,CACxB,WAAW,EACX,YAAY,EACZ,sBAAsB,EACtB,mBAAmB,EACnB,mBAAmB,EACnB,iBAAiB,EACjB,eAAe,CAChB,CAAC;AACJ,CAAC;AA3ED,4BA2EC;AAED,SAAS,yBAAyB;IAChC,yCAAyC;IACzC,eAAe,GAAG,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAC7C,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC;QAC3C,eAAe,EAAE,KAAK;QACtB,WAAW,EAAE,IAAI;QACjB,kBAAkB,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;QAC7C,iBAAiB,EAAE,MAAM,CAAC,iBAAiB,CAAC,KAAK;KAClD,CAAC,CACH,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB;IAC1B,IAAI,CAAC,aAAa,EAAE;QAClB,OAAO;KACR;IAED,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;IACxC,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;IAChC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAE/B,4BAA4B;IAC5B,eAAe,EAAE,CAAC;IAElB,uBAAuB;IACvB,eAAe,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAE5C,mBAAmB;IACnB,wBAAwB,CAAC,eAAe,CAAC,CAAC;IAE1C,mBAAmB;IACnB,iBAAiB,CAAC,OAAO,EAAE,CAAC;IAE5B,kCAAkC;IAClC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,4BAA4B,EAAE,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAEvG,sBAAsB;IACtB,MAAM,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAClG,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;QAC9B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,SAAS,eAAe,CAAC,MAAM,+BAA+B,eAAe,eAAe,CAC7F,CAAC;KACH;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAe;IACzC,MAAM,OAAO,GAAG,IAAI,GAAG,EAAoB,CAAC;IAE5C,0EAA0E;IAC1E,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;QAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAChC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;gBAC7B,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;aAC9B;YACD,OAAO,CAAC,GAAG,CAAC,WAAW,CAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACvC;IACH,CAAC,CAAC,CAAC;IAEH,0DAA0D;IAC1D,MAAM,eAAe,GAAqB,EAAE,CAAC;IAC7C,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,OAAO,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,OAAO,EAAE,EAAE;QACvC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,eAAe,CAAC,IAAI,CAAC;gBACnB,OAAO;gBACP,WAAW;gBACX,KAAK,EAAE,WAAW,CAAC,MAAM;gBACzB,UAAU,EAAE,UAAU,GAAG,gBAAgB,CAAC,MAAM;aACjD,CAAC,CAAC;YACH,UAAU,EAAE,CAAC;SACd;IACH,CAAC,CAAC,CAAC;IAEH,wCAAwC;IACxC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IAElD,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,SAAS,wBAAwB,CAAC,MAAwB;IACxD,IAAI,CAAC,aAAa,EAAE;QAClB,OAAO;KACR;IAED,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;QACvB,MAAM,cAAc,GAAG,eAAe,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAEzD,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAChD,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE,UAAU,EAAE,MAAM,CAAC,gBAAgB,CAAC,CACrE,CAAC;QAEF,aAAc,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,eAAe;IACtB,IAAI,CAAC,aAAa,EAAE;QAClB,OAAO;KACR;IAED,eAAe,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;QACvC,aAAc,CAAC,cAAc,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,eAAe,CAAC,UAAkB;IACzC,IAAI,CAAC,aAAa,IAAI,UAAU,IAAI,eAAe,CAAC,MAAM,EAAE;QAC1D,OAAO;KACR;IAED,iBAAiB,GAAG,UAAU,CAAC;IAC/B,sBAAsB,GAAG,CAAC,CAAC;IAE3B,MAAM,KAAK,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;IAC1C,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;QAChC,cAAc,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,qBAAqB,EAAE,CAAC;KACzB;AACH,CAAC;AAED,SAAS,sBAAsB;IAC7B,IAAI,CAAC,aAAa,IAAI,iBAAiB,IAAI,eAAe,CAAC,MAAM,EAAE;QACjE,OAAO;KACR;IAED,MAAM,KAAK,GAAG,eAAe,CAAC,iBAAiB,CAAC,CAAC;IACjD,sBAAsB,GAAG,CAAC,sBAAsB,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC;IACjF,cAAc,CAAC,KAAK,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC;IAC1D,qBAAqB,EAAE,CAAC;AAC1B,CAAC;AAED,SAAS,sBAAsB;IAC7B,IAAI,CAAC,aAAa,IAAI,iBAAiB,IAAI,eAAe,CAAC,MAAM,EAAE;QACjE,OAAO;KACR;IAED,MAAM,KAAK,GAAG,eAAe,CAAC,iBAAiB,CAAC,CAAC;IACjD,sBAAsB,GAAG,sBAAsB,KAAK,CAAC;QACnD,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC;QAC9B,CAAC,CAAC,sBAAsB,GAAG,CAAC,CAAC;IAC/B,cAAc,CAAC,KAAK,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC;IAC1D,qBAAqB,EAAE,CAAC;AAC1B,CAAC;AAED,SAAS,cAAc,CAAC,UAAkB;IACxC,IAAI,CAAC,aAAa,EAAE;QAClB,OAAO;KACR;IAED,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IACpD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEnD,aAAa,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACnE,aAAa,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AACzE,CAAC;AAED,SAAS,qBAAqB;IAC5B,IAAI,CAAC,aAAa,IAAI,iBAAiB,IAAI,eAAe,CAAC,MAAM,EAAE;QACjE,OAAO;KACR;IAED,MAAM,KAAK,GAAG,eAAe,CAAC,iBAAiB,CAAC,CAAC;IACjD,MAAM,OAAO,GAAG,aAAa,sBAAsB,GAAG,CAAC,OAAO,KAAK,CAAC,WAAW,CAAC,MAAM,MAAM,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAEpH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,OAAO,EACP,UAAU,EACV,MAAM,EACN,aAAa,EACb,WAAW,CACZ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QACjB,QAAQ,SAAS,EAAE;YACjB,KAAK,UAAU;gBACb,sBAAsB,EAAE,CAAC;gBACzB,MAAM;YACR,KAAK,MAAM;gBACT,sBAAsB,EAAE,CAAC;gBACzB,MAAM;YACR,KAAK,aAAa;gBAChB,iBAAiB,EAAE,CAAC;gBACpB,MAAM;YACR,KAAK,WAAW;gBACd,eAAe,EAAE,CAAC;gBAClB,MAAM;SACT;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB;IACxB,IAAI,CAAC,aAAa,IAAI,iBAAiB,IAAI,eAAe,CAAC,MAAM,EAAE;QACjE,OAAO;KACR;IAED,MAAM,KAAK,GAAG,eAAe,CAAC,iBAAiB,CAAC,CAAC;IACjD,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC;IAE7D,oCAAoC;IACpC,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;IACxC,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACrE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;IAEnD,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;QACzC,uBAAuB;QACvB,UAAU,CAAC,GAAG,EAAE,CAAC,mBAAmB,EAAE,EAAE,GAAG,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,eAAe;IACtB,IAAI,CAAC,aAAa,IAAI,iBAAiB,IAAI,eAAe,CAAC,MAAM,EAAE;QACjE,OAAO;KACR;IAED,MAAM,KAAK,GAAG,eAAe,CAAC,iBAAiB,CAAC,CAAC;IACjD,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC;IAE7D,4CAA4C;IAC5C,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC;IACpD,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC;IAEvC,qDAAqD;IACrD,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,EAAE;QACjC,eAAe,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QAC7C,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;YAChC,eAAe,EAAE,CAAC;SACnB;aAAM;YACL,sCAAsC;YACtC,IAAI,iBAAiB,IAAI,eAAe,CAAC,MAAM,EAAE;gBAC/C,iBAAiB,GAAG,CAAC,CAAC;aACvB;YACD,eAAe,CAAC,iBAAiB,CAAC,CAAC;SACpC;KACF;SAAM;QACL,oCAAoC;QACpC,IAAI,sBAAsB,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE;YACtD,sBAAsB,GAAG,CAAC,CAAC;SAC5B;QACD,cAAc,CAAC,KAAK,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC;QAC1D,qBAAqB,EAAE,CAAC;KACzB;IAED,kCAAkC;IAClC,eAAe,EAAE,CAAC;IAClB,wBAAwB,CAAC,eAAe,CAAC,CAAC;IAC1C,iBAAiB,CAAC,OAAO,EAAE,CAAC;AAC9B,CAAC;AAED,SAAgB,UAAU;IACxB,eAAe,EAAE,CAAC;IAClB,eAAe,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC;AACtE,CAAC;AAHD,gCAGC"}