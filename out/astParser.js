"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ASTParser = void 0;
const vscode = __importStar(require("vscode"));
// Note: Tree-sitter imports would be here in a real implementation
// For now, we'll use a simplified AST approach
class ASTParser {
    constructor() {
        this.languageConfigs = new Map();
        this.initializeLanguageConfigs();
    }
    initializeLanguageConfigs() {
        // JavaScript/TypeScript
        this.languageConfigs.set('javascript', {
            id: 'javascript',
            extensions: ['.js', '.jsx'],
            trivialPatterns: [
                /^const\s+\w+\s*=\s*document\./,
                /^import\s+.*from\s+['"].*['"];?$/,
                /^export\s+.*$/,
                /^\/\/.*$/,
                /^\/\*.*\*\/$/
            ],
            blockMinLines: 3
        });
        this.languageConfigs.set('typescript', {
            id: 'typescript',
            extensions: ['.ts', '.tsx'],
            trivialPatterns: [
                /^const\s+\w+\s*=\s*document\./,
                /^import\s+.*from\s+['"].*['"];?$/,
                /^export\s+.*$/,
                /^interface\s+\w+\s*{$/,
                /^type\s+\w+\s*=.*$/
            ],
            blockMinLines: 3
        });
        // Python
        this.languageConfigs.set('python', {
            id: 'python',
            extensions: ['.py'],
            trivialPatterns: [
                /^import\s+.*$/,
                /^from\s+.*import\s+.*$/,
                /^#.*$/,
                /^""".*"""$/,
                /^def\s+__\w+__\(.*\):$/
            ],
            blockMinLines: 3
        });
        // Java
        this.languageConfigs.set('java', {
            id: 'java',
            extensions: ['.java'],
            trivialPatterns: [
                /^import\s+.*$/,
                /^package\s+.*$/,
                /^\/\/.*$/,
                /^\/\*.*\*\/$/,
                /^public\s+class\s+\w+.*{$/
            ],
            blockMinLines: 4
        });
        // Add more languages as needed
    }
    async parseFile(filePath) {
        try {
            const document = await vscode.workspace.openTextDocument(filePath);
            const language = this.detectLanguage(filePath, document.languageId);
            if (!language) {
                return null;
            }
            const content = document.getText();
            const ast = this.parseContent(content, language);
            const blocks = this.extractCodeBlocks(content, language, filePath);
            return {
                filePath,
                language: language.id,
                ast,
                blocks
            };
        }
        catch (error) {
            console.error(`Error parsing file ${filePath}:`, error);
            return null;
        }
    }
    detectLanguage(filePath, languageId) {
        // First try by VS Code language ID
        if (this.languageConfigs.has(languageId)) {
            return this.languageConfigs.get(languageId);
        }
        // Fallback to file extension
        const extension = '.' + filePath.split('.').pop()?.toLowerCase();
        for (const config of this.languageConfigs.values()) {
            if (config.extensions.includes(extension)) {
                return config;
            }
        }
        return null;
    }
    parseContent(content, language) {
        // Simplified AST parsing - in a real implementation, this would use Tree-sitter
        const lines = content.split('\n');
        return {
            type: 'program',
            startPosition: { row: 0, column: 0 },
            endPosition: { row: lines.length - 1, column: lines[lines.length - 1]?.length || 0 },
            children: this.parseStatements(lines, language),
            text: content
        };
    }
    parseStatements(lines, language) {
        const statements = [];
        let currentStatement = [];
        let startLine = 0;
        let braceCount = 0;
        let inString = false;
        let stringChar = '';
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (line === '' || this.isTrivialLine(line, language)) {
                if (currentStatement.length > 0) {
                    statements.push(this.createStatementNode(currentStatement, startLine, i - 1));
                    currentStatement = [];
                }
                continue;
            }
            if (currentStatement.length === 0) {
                startLine = i;
            }
            currentStatement.push(lines[i]);
            // Simple brace counting for block detection
            for (const char of line) {
                if (!inString && (char === '"' || char === "'" || char === '`')) {
                    inString = true;
                    stringChar = char;
                }
                else if (inString && char === stringChar) {
                    inString = false;
                    stringChar = '';
                }
                else if (!inString) {
                    if (char === '{')
                        braceCount++;
                    else if (char === '}')
                        braceCount--;
                }
            }
            // End statement on semicolon, closing brace, or specific patterns
            if (!inString && (line.endsWith(';') ||
                line.endsWith('}') ||
                (braceCount === 0 && currentStatement.length >= language.blockMinLines))) {
                statements.push(this.createStatementNode(currentStatement, startLine, i));
                currentStatement = [];
                braceCount = 0;
            }
        }
        // Handle remaining statement
        if (currentStatement.length > 0) {
            statements.push(this.createStatementNode(currentStatement, startLine, lines.length - 1));
        }
        return statements;
    }
    createStatementNode(lines, startLine, endLine) {
        return {
            type: 'statement',
            startPosition: { row: startLine, column: 0 },
            endPosition: { row: endLine, column: lines[lines.length - 1]?.length || 0 },
            text: lines.join('\n')
        };
    }
    isTrivialLine(line, language) {
        return language.trivialPatterns.some(pattern => pattern.test(line));
    }
    extractCodeBlocks(content, language, filePath) {
        const lines = content.split('\n');
        const blocks = [];
        let currentBlock = [];
        let startLine = 0;
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (line === '' || this.isTrivialLine(line, language)) {
                if (currentBlock.length >= language.blockMinLines) {
                    blocks.push({
                        content: currentBlock.join('\n'),
                        startLine,
                        endLine: i - 1,
                        filePath,
                        hash: this.hashContent(currentBlock.join('\n'))
                    });
                }
                currentBlock = [];
                continue;
            }
            if (currentBlock.length === 0) {
                startLine = i;
            }
            currentBlock.push(lines[i]);
        }
        // Handle final block
        if (currentBlock.length >= language.blockMinLines) {
            blocks.push({
                content: currentBlock.join('\n'),
                startLine,
                endLine: lines.length - 1,
                filePath,
                hash: this.hashContent(currentBlock.join('\n'))
            });
        }
        return blocks;
    }
    hashContent(content) {
        // Simple hash function - in production, use a proper hash library
        let hash = 0;
        for (let i = 0; i < content.length; i++) {
            const char = content.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }
    normalizeCode(content, language) {
        // Normalize code for better comparison
        return content
            .replace(/\s+/g, ' ') // Normalize whitespace
            .replace(/\/\/.*$/gm, '') // Remove single-line comments
            .replace(/\/\*[\s\S]*?\*\//g, '') // Remove multi-line comments
            .replace(/['"`]/g, '"') // Normalize quotes
            .trim();
    }
}
exports.ASTParser = ASTParser;
//# sourceMappingURL=astParser.js.map