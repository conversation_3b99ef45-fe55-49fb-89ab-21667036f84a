{"version": 3, "file": "extension.test.js", "sourceRoot": "", "sources": ["../../../src/test/suite/extension.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,+CAAiC;AACjC,iEAA8D;AAG9D,KAAK,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACjC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;IAEzD,IAAI,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACpC,MAAM,QAAQ,GAAG,IAAI,uCAAkB,EAAE,CAAC;QAE1C,MAAM,MAAM,GAAc;YACxB,OAAO,EAAE;;;QAGP;YACF,SAAS,EAAE,CAAC;YACZ,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE,gBAAgB;YAC1B,IAAI,EAAE,OAAO;SACd,CAAC;QAEF,MAAM,MAAM,GAAc;YACxB,OAAO,EAAE;;;QAGP;YACF,SAAS,EAAE,EAAE;YACb,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,gBAAgB;YAC1B,IAAI,EAAE,OAAO;SACd,CAAC;QAEF,MAAM,UAAU,GAAG,QAAQ,CAAC,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAE9D,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,GAAG,GAAG,EAAE,+CAA+C,CAAC,CAAC;QACxF,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,aAAa,EAAE,oDAAoD,CAAC,CAAC;IAC5F,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,wBAAwB,EAAE,GAAG,EAAE;QAClC,MAAM,QAAQ,GAAG,IAAI,uCAAkB,EAAE,CAAC;QAE1C,MAAM,MAAM,GAAc;YACxB,OAAO,EAAE,kDAAkD;YAC3D,SAAS,EAAE,CAAC;YACZ,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE,gBAAgB;YAC1B,IAAI,EAAE,OAAO;SACd,CAAC;QAEF,MAAM,MAAM,GAAc;YACxB,OAAO,EAAE,mDAAmD;YAC5D,SAAS,EAAE,CAAC;YACZ,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE,gBAAgB;YAC1B,IAAI,EAAE,OAAO;SACd,CAAC;QAEF,MAAM,UAAU,GAAG,QAAQ,CAAC,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAE9D,MAAM,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,aAAa,EAAE,qEAAqE,CAAC,CAAC;IAC9G,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}