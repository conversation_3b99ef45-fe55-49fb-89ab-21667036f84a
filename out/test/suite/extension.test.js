"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const vscode = __importStar(require("vscode"));
const similarityAnalyzer_1 = require("../../similarityAnalyzer");
suite('Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');
    test('Similarity Analyzer Test', () => {
        const analyzer = new similarityAnalyzer_1.SimilarityAnalyzer();
        const block1 = {
            content: `function calculateSum(a, b) {
        const result = a + b;
        return result;
      }`,
            startLine: 1,
            endLine: 4,
            filePath: '/test/file1.js',
            hash: 'hash1'
        };
        const block2 = {
            content: `function calculateTotal(x, y) {
        const sum = x + y;
        return sum;
      }`,
            startLine: 10,
            endLine: 13,
            filePath: '/test/file2.js',
            hash: 'hash2'
        };
        const similarity = analyzer.analyzeSimilarity(block1, block2);
        assert.ok(similarity.similarity > 0.7, 'Similar functions should have high similarity');
        assert.ok(similarity.isSignificant, 'Similar functions should be flagged as significant');
    });
    test('Trivial Code Detection', () => {
        const analyzer = new similarityAnalyzer_1.SimilarityAnalyzer();
        const block1 = {
            content: `const element = document.getElementById('test');`,
            startLine: 1,
            endLine: 1,
            filePath: '/test/file1.js',
            hash: 'hash1'
        };
        const block2 = {
            content: `const button = document.getElementById('button');`,
            startLine: 5,
            endLine: 5,
            filePath: '/test/file2.js',
            hash: 'hash2'
        };
        const similarity = analyzer.analyzeSimilarity(block1, block2);
        assert.ok(!similarity.isSignificant, 'Trivial DOM queries should not be flagged as significant duplicates');
    });
});
//# sourceMappingURL=extension.test.js.map