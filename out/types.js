"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuplicateTreeItem = void 0;
const vscode = __importStar(require("vscode"));
class DuplicateTreeItem extends vscode.TreeItem {
    constructor(label, collapsibleState, duplicateGroup, codeBlock) {
        super(label, collapsibleState);
        this.label = label;
        this.collapsibleState = collapsibleState;
        this.duplicateGroup = duplicateGroup;
        this.codeBlock = codeBlock;
        if (codeBlock) {
            this.command = {
                command: 'duplicateFinder.navigateToDuplicate',
                title: 'Navigate to Duplicate',
                arguments: [codeBlock]
            };
            this.tooltip = `${codeBlock.filePath}:${codeBlock.startLine}-${codeBlock.endLine}`;
            this.contextValue = 'duplicateBlock';
        }
        else if (duplicateGroup) {
            this.tooltip = `${duplicateGroup.blocks.length} duplicates, ${duplicateGroup.lineCount} lines, ${Math.round(duplicateGroup.similarity * 100)}% similar`;
            this.contextValue = 'duplicateGroup';
        }
    }
}
exports.DuplicateTreeItem = DuplicateTreeItem;
//# sourceMappingURL=types.js.map