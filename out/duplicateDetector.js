"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuplicateDetector = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const astParser_1 = require("./astParser");
const similarityAnalyzer_1 = require("./similarityAnalyzer");
class DuplicateDetector {
    constructor() {
        this.astParser = new astParser_1.ASTParser();
        this.similarityAnalyzer = new similarityAnalyzer_1.SimilarityAnalyzer();
    }
    async scanWorkspace() {
        const config = this.getConfig();
        const files = await this.getFilesToScan(config);
        console.log(`Scanning ${files.length} files for duplicates...`);
        // Parse all files and extract code blocks
        const allBlocks = [];
        for (const file of files) {
            try {
                const parsedFile = await this.astParser.parseFile(file);
                if (parsedFile && parsedFile.blocks.length > 0) {
                    allBlocks.push(...parsedFile.blocks);
                }
            }
            catch (error) {
                console.error(`Error parsing file ${file}:`, error);
            }
        }
        console.log(`Extracted ${allBlocks.length} code blocks`);
        // Find duplicate groups
        const duplicateGroups = this.findDuplicateGroups(allBlocks, config);
        console.log(`Found ${duplicateGroups.length} duplicate groups`);
        return duplicateGroups;
    }
    async getFilesToScan(config) {
        if (!vscode.workspace.workspaceFolders) {
            return [];
        }
        const files = [];
        const supportedExtensions = ['.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cs', '.cpp', '.c', '.go', '.rs', '.php'];
        for (const folder of vscode.workspace.workspaceFolders) {
            const pattern = new vscode.RelativePattern(folder, '**/*');
            const foundFiles = await vscode.workspace.findFiles(pattern);
            for (const file of foundFiles) {
                const filePath = file.fsPath;
                const extension = path.extname(filePath).toLowerCase();
                // Check if file extension is supported
                if (!supportedExtensions.includes(extension)) {
                    continue;
                }
                // Check exclude patterns
                if (this.isExcluded(filePath, config.excludePatterns)) {
                    continue;
                }
                files.push(filePath);
            }
        }
        return files;
    }
    isExcluded(filePath, excludePatterns) {
        const relativePath = vscode.workspace.asRelativePath(filePath);
        return excludePatterns.some(pattern => {
            // Convert glob pattern to regex
            const regexPattern = pattern
                .replace(/\*\*/g, '.*')
                .replace(/\*/g, '[^/]*')
                .replace(/\?/g, '[^/]');
            const regex = new RegExp(regexPattern);
            return regex.test(relativePath);
        });
    }
    findDuplicateGroups(blocks, config) {
        const groups = [];
        const processed = new Set();
        for (let i = 0; i < blocks.length; i++) {
            const block1 = blocks[i];
            if (processed.has(block1.hash)) {
                continue;
            }
            // Check if block meets minimum line requirement
            const lineCount = block1.endLine - block1.startLine + 1;
            if (lineCount < config.minLines) {
                continue;
            }
            const duplicates = [block1];
            processed.add(block1.hash);
            // Find similar blocks
            for (let j = i + 1; j < blocks.length; j++) {
                const block2 = blocks[j];
                if (processed.has(block2.hash)) {
                    continue;
                }
                // Skip if blocks are from the same file and too close
                if (block1.filePath === block2.filePath) {
                    const gap = Math.abs(block1.startLine - block2.startLine);
                    if (gap < config.minLines) {
                        continue;
                    }
                }
                const similarity = this.similarityAnalyzer.analyzeSimilarity(block1, block2);
                if (similarity.isSignificant && similarity.similarity >= config.similarityThreshold) {
                    duplicates.push(block2);
                    processed.add(block2.hash);
                }
            }
            // Create group if we found duplicates
            if (duplicates.length > 1) {
                const avgSimilarity = this.calculateAverageSimilarity(duplicates);
                groups.push({
                    id: this.generateGroupId(duplicates),
                    blocks: duplicates,
                    similarity: avgSimilarity,
                    lineCount,
                    language: this.detectLanguageFromPath(block1.filePath)
                });
            }
        }
        // Sort groups by severity (line count * number of duplicates * similarity)
        groups.sort((a, b) => {
            const severityA = a.lineCount * a.blocks.length * a.similarity;
            const severityB = b.lineCount * b.blocks.length * b.similarity;
            return severityB - severityA;
        });
        return groups;
    }
    calculateAverageSimilarity(blocks) {
        if (blocks.length < 2) {
            return 1.0;
        }
        let totalSimilarity = 0;
        let comparisons = 0;
        for (let i = 0; i < blocks.length; i++) {
            for (let j = i + 1; j < blocks.length; j++) {
                const similarity = this.similarityAnalyzer.analyzeSimilarity(blocks[i], blocks[j]);
                totalSimilarity += similarity.similarity;
                comparisons++;
            }
        }
        return comparisons > 0 ? totalSimilarity / comparisons : 1.0;
    }
    generateGroupId(blocks) {
        // Generate a unique ID based on the first block's hash and file count
        const firstBlock = blocks[0];
        const fileCount = new Set(blocks.map(b => b.filePath)).size;
        return `${firstBlock.hash}_${fileCount}_${blocks.length}`;
    }
    detectLanguageFromPath(filePath) {
        const extension = path.extname(filePath).toLowerCase();
        const languageMap = {
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.py': 'python',
            '.java': 'java',
            '.cs': 'csharp',
            '.cpp': 'cpp',
            '.c': 'c',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php'
        };
        return languageMap[extension] || 'unknown';
    }
    getConfig() {
        const config = vscode.workspace.getConfiguration('duplicateFinder');
        return {
            minLines: config.get('minLines', 5),
            similarityThreshold: config.get('similarityThreshold', 0.8),
            excludePatterns: config.get('excludePatterns', [
                '**/node_modules/**',
                '**/dist/**',
                '**/build/**',
                '**/*.min.js'
            ]),
            enabledLanguages: config.get('enabledLanguages', [
                'javascript',
                'typescript',
                'python',
                'java',
                'csharp',
                'cpp',
                'c',
                'go',
                'rust',
                'php'
            ]),
            autoScan: config.get('autoScan', true)
        };
    }
}
exports.DuplicateDetector = DuplicateDetector;
//# sourceMappingURL=duplicateDetector.js.map