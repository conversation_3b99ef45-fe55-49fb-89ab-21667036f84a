{"version": 3, "file": "astParser.js", "sourceRoot": "", "sources": ["../src/astParser.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,mEAAmE;AACnE,+CAA+C;AAE/C,MAAa,SAAS;IAGpB;QAFQ,oBAAe,GAAgC,IAAI,GAAG,EAAE,CAAC;QAG/D,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IAEO,yBAAyB;QAC/B,wBAAwB;QACxB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,YAAY,EAAE;YACrC,EAAE,EAAE,YAAY;YAChB,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;YAC3B,eAAe,EAAE;gBACf,+BAA+B;gBAC/B,kCAAkC;gBAClC,eAAe;gBACf,UAAU;gBACV,cAAc;aACf;YACD,aAAa,EAAE,CAAC;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,YAAY,EAAE;YACrC,EAAE,EAAE,YAAY;YAChB,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;YAC3B,eAAe,EAAE;gBACf,+BAA+B;gBAC/B,kCAAkC;gBAClC,eAAe;gBACf,uBAAuB;gBACvB,oBAAoB;aACrB;YACD,aAAa,EAAE,CAAC;SACjB,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE;YACjC,EAAE,EAAE,QAAQ;YACZ,UAAU,EAAE,CAAC,KAAK,CAAC;YACnB,eAAe,EAAE;gBACf,eAAe;gBACf,wBAAwB;gBACxB,OAAO;gBACP,YAAY;gBACZ,wBAAwB;aACzB;YACD,aAAa,EAAE,CAAC;SACjB,CAAC,CAAC;QAEH,OAAO;QACP,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE;YAC/B,EAAE,EAAE,MAAM;YACV,UAAU,EAAE,CAAC,OAAO,CAAC;YACrB,eAAe,EAAE;gBACf,eAAe;gBACf,gBAAgB;gBAChB,UAAU;gBACV,cAAc;gBACd,2BAA2B;aAC5B;YACD,aAAa,EAAE,CAAC;SACjB,CAAC,CAAC;QAEH,+BAA+B;IACjC,CAAC;IAEM,KAAK,CAAC,SAAS,CAAC,QAAgB;QACrC,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;YAEpE,IAAI,CAAC,QAAQ,EAAE;gBACb,OAAO,IAAI,CAAC;aACb;YAED,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YACnC,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACjD,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEnE,OAAO;gBACL,QAAQ;gBACR,QAAQ,EAAE,QAAQ,CAAC,EAAE;gBACrB,GAAG;gBACH,MAAM;aACP,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,sBAAsB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAEO,cAAc,CAAC,QAAgB,EAAE,UAAkB;QACzD,mCAAmC;QACnC,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YACxC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC;SAC9C;QAED,6BAA6B;QAC7B,MAAM,SAAS,GAAG,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,CAAC;QACjE,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE;YAClD,IAAI,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;gBACzC,OAAO,MAAM,CAAC;aACf;SACF;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,YAAY,CAAC,OAAe,EAAE,QAAwB;QAC5D,gFAAgF;QAChF,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAElC,OAAO;YACL,IAAI,EAAE,SAAS;YACf,aAAa,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;YACpC,WAAW,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,EAAE;YACpF,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,CAAC;YAC/C,IAAI,EAAE,OAAO;SACd,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,KAAe,EAAE,QAAwB;QAC/D,MAAM,UAAU,GAAc,EAAE,CAAC;QACjC,IAAI,gBAAgB,GAAa,EAAE,CAAC;QACpC,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,UAAU,GAAG,EAAE,CAAC;QAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAE7B,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;gBACrD,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC/B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC9E,gBAAgB,GAAG,EAAE,CAAC;iBACvB;gBACD,SAAS;aACV;YAED,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACjC,SAAS,GAAG,CAAC,CAAC;aACf;YAED,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAEhC,4CAA4C;YAC5C,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;gBACvB,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE;oBAC/D,QAAQ,GAAG,IAAI,CAAC;oBAChB,UAAU,GAAG,IAAI,CAAC;iBACnB;qBAAM,IAAI,QAAQ,IAAI,IAAI,KAAK,UAAU,EAAE;oBAC1C,QAAQ,GAAG,KAAK,CAAC;oBACjB,UAAU,GAAG,EAAE,CAAC;iBACjB;qBAAM,IAAI,CAAC,QAAQ,EAAE;oBACpB,IAAI,IAAI,KAAK,GAAG;wBAAE,UAAU,EAAE,CAAC;yBAC1B,IAAI,IAAI,KAAK,GAAG;wBAAE,UAAU,EAAE,CAAC;iBACrC;aACF;YAED,kEAAkE;YAClE,IAAI,CAAC,QAAQ,IAAI,CACf,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAClB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAClB,CAAC,UAAU,KAAK,CAAC,IAAI,gBAAgB,CAAC,MAAM,IAAI,QAAQ,CAAC,aAAa,CAAC,CACxE,EAAE;gBACD,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC1E,gBAAgB,GAAG,EAAE,CAAC;gBACtB,UAAU,GAAG,CAAC,CAAC;aAChB;SACF;QAED,6BAA6B;QAC7B,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,SAAS,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;SAC1F;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,mBAAmB,CAAC,KAAe,EAAE,SAAiB,EAAE,OAAe;QAC7E,OAAO;YACL,IAAI,EAAE,WAAW;YACjB,aAAa,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,EAAE;YAC5C,WAAW,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,EAAE;YAC3E,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;SACvB,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,IAAY,EAAE,QAAwB;QAC1D,OAAO,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACtE,CAAC;IAEO,iBAAiB,CAAC,OAAe,EAAE,QAAwB,EAAE,QAAgB;QACnF,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,MAAM,MAAM,GAAU,EAAE,CAAC;QACzB,IAAI,YAAY,GAAa,EAAE,CAAC;QAChC,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAE7B,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;gBACrD,IAAI,YAAY,CAAC,MAAM,IAAI,QAAQ,CAAC,aAAa,EAAE;oBACjD,MAAM,CAAC,IAAI,CAAC;wBACV,OAAO,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;wBAChC,SAAS;wBACT,OAAO,EAAE,CAAC,GAAG,CAAC;wBACd,QAAQ;wBACR,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBAChD,CAAC,CAAC;iBACJ;gBACD,YAAY,GAAG,EAAE,CAAC;gBAClB,SAAS;aACV;YAED,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7B,SAAS,GAAG,CAAC,CAAC;aACf;YACD,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7B;QAED,qBAAqB;QACrB,IAAI,YAAY,CAAC,MAAM,IAAI,QAAQ,CAAC,aAAa,EAAE;YACjD,MAAM,CAAC,IAAI,CAAC;gBACV,OAAO,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;gBAChC,SAAS;gBACT,OAAO,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC;gBACzB,QAAQ;gBACR,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAChD,CAAC,CAAC;SACJ;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,WAAW,CAAC,OAAe;QACjC,kEAAkE;QAClE,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,MAAM,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YACnC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,4BAA4B;SACjD;QACD,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;IAEM,aAAa,CAAC,OAAe,EAAE,QAAwB;QAC5D,uCAAuC;QACvC,OAAO,OAAO;aACX,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,uBAAuB;aAC5C,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,8BAA8B;aACvD,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC,6BAA6B;aAC9D,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,mBAAmB;aAC1C,IAAI,EAAE,CAAC;IACZ,CAAC;CACF;AAhQD,8BAgQC"}