{"version": 3, "file": "decorationManager.js", "sourceRoot": "", "sources": ["../src/decorationManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,MAAa,iBAAiB;IAI5B;QAFQ,gBAAW,GAAkC,IAAI,GAAG,EAAE,CAAC;QAG7D,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC;YACjE,eAAe,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,qCAAqC,CAAC;YAC7E,WAAW,EAAE,KAAK;YAClB,WAAW,EAAE,OAAO;YACpB,WAAW,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,iCAAiC,CAAC;YACrE,kBAAkB,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,wCAAwC,CAAC;YACnF,iBAAiB,EAAE,MAAM,CAAC,iBAAiB,CAAC,KAAK;YACjD,WAAW,EAAE,IAAI;YACjB,KAAK,EAAE;gBACL,WAAW,EAAE,KAAK;gBAClB,KAAK,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,+BAA+B,CAAC;aAC9D;SACF,CAAC,CAAC;QAEH,yDAAyD;QACzD,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,MAAM,CAAC,EAAE;YACjD,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;aACtC;QACH,CAAC,CAAC,CAAC;QAEH,yDAAyD;QACzD,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;YAC/C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC,CAAC;YACzF,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;aACtC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,iBAAiB,CAAC,eAAiC;QACxD,6BAA6B;QAC7B,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,yBAAyB;QACzB,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE;YACnC,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;gBAChC,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;gBAEhC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;oBACnC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;iBACpC;gBAED,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAC5B,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,EACvC,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,CAC5D,CAAC;gBAEF,MAAM,cAAc,GAAmB;oBACrC,KAAK;oBACL,cAAc,EAAE,KAAK;oBACrB,SAAS,EAAE,KAAK;iBACjB,CAAC;gBAEF,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;aACtD;SACF;QAED,iDAAiD;QACjD,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE;YACrD,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;SACtC;IACH,CAAC;IAEM,mBAAmB;QACxB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAEzB,6CAA6C;QAC7C,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE;YACrD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;SAChD;IACH,CAAC;IAEO,uBAAuB,CAAC,MAAyB;QACvD,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;QAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEvD,IAAI,CAAC,eAAe,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;YACpD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;YAC/C,OAAO;SACR;QAED,gDAAgD;QAChD,MAAM,iBAAiB,GAA+B,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YACrF,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAEzD,OAAO;gBACL,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,YAAY;aACb,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;IAChE,CAAC;IAEO,kBAAkB,CAAC,UAA0B;QACnD,MAAM,KAAK,GAAG,UAAU,CAAC,cAAc,CAAC;QACxC,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC;QAEnC,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;QAC7C,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;QAE1B,QAAQ,CAAC,cAAc,CAAC,iCAAiC,CAAC,CAAC;QAC3D,QAAQ,CAAC,cAAc,CAAC,qBAAqB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QACtF,QAAQ,CAAC,cAAc,CAAC,gBAAgB,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC;QAC7D,QAAQ,CAAC,cAAc,CAAC,mBAAmB,KAAK,CAAC,QAAQ,IAAI,CAAC,CAAC;QAC/D,QAAQ,CAAC,cAAc,CAAC,2BAA2B,KAAK,CAAC,MAAM,CAAC,MAAM,MAAM,CAAC,CAAC;QAE9E,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;QAElD,KAAK,MAAM,UAAU,IAAI,KAAK,CAAC,MAAM,EAAE;YACrC,IAAI,UAAU,KAAK,KAAK,EAAE;gBACxB,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAC1E,MAAM,OAAO,GAAG,+CAA+C,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC;gBAChH,QAAQ,CAAC,cAAc,CAAC,MAAM,YAAY,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,OAAO,KAAK,OAAO,KAAK,CAAC,CAAC;aAC5G;SACF;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,OAAO;QACZ,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;CACF;AAlID,8CAkIC"}