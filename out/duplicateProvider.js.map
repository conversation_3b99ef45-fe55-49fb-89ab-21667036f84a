{"version": 3, "file": "duplicateProvider.js", "sourceRoot": "", "sources": ["../src/duplicateProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAC7B,mCAAuE;AAEvE,MAAa,iBAAiB;IAM5B;QALQ,yBAAoB,GAAqE,IAAI,MAAM,CAAC,YAAY,EAA+C,CAAC;QAC/J,wBAAmB,GAA8D,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAElH,oBAAe,GAAqB,EAAE,CAAC;IAEhC,CAAC;IAET,gBAAgB,CAAC,MAAwB;QAC9C,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;QAC9B,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEM,KAAK;QACV,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAED,WAAW,CAAC,OAA0B;QACpC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,WAAW,CAAC,OAA2B;QACrC,IAAI,CAAC,OAAO,EAAE;YACZ,qCAAqC;YACrC,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;SACvD;aAAM,IAAI,OAAO,CAAC,cAAc,EAAE;YACjC,iDAAiD;YACjD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC;SAC7E;aAAM;YACL,4BAA4B;YAC5B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC5B;IACH,CAAC;IAEO,sBAAsB;QAC5B,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAC/C,MAAM,KAAK,GAAG,SAAS,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,MAAM,CAAC,MAAM,gBAAgB,KAAK,CAAC,SAAS,SAAS,CAAC;YACjG,MAAM,IAAI,GAAG,IAAI,yBAAiB,CAChC,KAAK,EACL,MAAM,CAAC,wBAAwB,CAAC,QAAQ,EACxC,KAAK,CACN,CAAC;YAEF,2BAA2B;YAC3B,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAChD,IAAI,CAAC,WAAW,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,WAAW,CAAC;YAEpE,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,KAAqB;QAClD,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM,KAAK,GAAG,GAAG,QAAQ,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAEhE,MAAM,IAAI,GAAG,IAAI,yBAAiB,CAChC,KAAK,EACL,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,SAAS,EACT,KAAK,CACN,CAAC;YAEF,8BAA8B;YAC9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACjD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAExD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,WAAW,CAAC,QAAgB;QAClC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAEvD,MAAM,OAAO,GAA8B;YACzC,KAAK,EAAE,eAAe;YACtB,MAAM,EAAE,eAAe;YACvB,KAAK,EAAE,eAAe;YACtB,MAAM,EAAE,eAAe;YACvB,KAAK,EAAE,eAAe;YACtB,OAAO,EAAE,cAAc;YACvB,KAAK,EAAE,cAAc;YACrB,MAAM,EAAE,eAAe;YACvB,IAAI,EAAE,eAAe;YACrB,KAAK,EAAE,eAAe;YACtB,KAAK,EAAE,eAAe;YACtB,MAAM,EAAE,eAAe;SACxB,CAAC;QAEF,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,aAAa,CAAC;QACrD,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAEO,eAAe,CAAC,QAAgB;QACtC,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE;YACrC,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;SAClD;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;CACF;AApGD,8CAoGC"}