"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DecorationManager = void 0;
const vscode = __importStar(require("vscode"));
class DecorationManager {
    constructor() {
        this.decorations = new Map();
        this.decorationType = vscode.window.createTextEditorDecorationType({
            backgroundColor: new vscode.ThemeColor('duplicateFinder.duplicateBackground'),
            borderWidth: '1px',
            borderStyle: 'solid',
            borderColor: new vscode.ThemeColor('duplicateFinder.duplicateBorder'),
            overviewRulerColor: new vscode.ThemeColor('duplicateFinder.duplicateOverviewRuler'),
            overviewRulerLane: vscode.OverviewRulerLane.Right,
            isWholeLine: true,
            after: {
                contentText: ' 🔄',
                color: new vscode.ThemeColor('duplicateFinder.duplicateIcon')
            }
        });
        // Listen for active editor changes to update decorations
        vscode.window.onDidChangeActiveTextEditor(editor => {
            if (editor) {
                this.updateEditorDecorations(editor);
            }
        });
        // Listen for text document changes to update decorations
        vscode.workspace.onDidChangeTextDocument(event => {
            const editor = vscode.window.visibleTextEditors.find(e => e.document === event.document);
            if (editor) {
                this.updateEditorDecorations(editor);
            }
        });
    }
    updateDecorations(duplicateGroups) {
        // Clear existing decorations
        this.clearAllDecorations();
        // Create new decorations
        for (const group of duplicateGroups) {
            for (const block of group.blocks) {
                const filePath = block.filePath;
                if (!this.decorations.has(filePath)) {
                    this.decorations.set(filePath, []);
                }
                const range = new vscode.Range(new vscode.Position(block.startLine, 0), new vscode.Position(block.endLine, Number.MAX_SAFE_INTEGER));
                const decorationInfo = {
                    range,
                    duplicateGroup: group,
                    codeBlock: block
                };
                this.decorations.get(filePath).push(decorationInfo);
            }
        }
        // Apply decorations to currently visible editors
        for (const editor of vscode.window.visibleTextEditors) {
            this.updateEditorDecorations(editor);
        }
    }
    clearAllDecorations() {
        this.decorations.clear();
        // Clear decorations from all visible editors
        for (const editor of vscode.window.visibleTextEditors) {
            editor.setDecorations(this.decorationType, []);
        }
    }
    updateEditorDecorations(editor) {
        const filePath = editor.document.uri.fsPath;
        const fileDecorations = this.decorations.get(filePath);
        if (!fileDecorations || fileDecorations.length === 0) {
            editor.setDecorations(this.decorationType, []);
            return;
        }
        // Create decoration options with hover messages
        const decorationOptions = fileDecorations.map(decoration => {
            const hoverMessage = this.createHoverMessage(decoration);
            return {
                range: decoration.range,
                hoverMessage
            };
        });
        editor.setDecorations(this.decorationType, decorationOptions);
    }
    createHoverMessage(decoration) {
        const group = decoration.duplicateGroup;
        const block = decoration.codeBlock;
        const markdown = new vscode.MarkdownString();
        markdown.isTrusted = true;
        markdown.appendMarkdown(`**Duplicate Code Detected**\n\n`);
        markdown.appendMarkdown(`- **Similarity**: ${Math.round(group.similarity * 100)}%\n`);
        markdown.appendMarkdown(`- **Lines**: ${group.lineCount}\n`);
        markdown.appendMarkdown(`- **Language**: ${group.language}\n`);
        markdown.appendMarkdown(`- **Total Duplicates**: ${group.blocks.length}\n\n`);
        markdown.appendMarkdown(`**Other Locations:**\n`);
        for (const otherBlock of group.blocks) {
            if (otherBlock !== block) {
                const relativePath = vscode.workspace.asRelativePath(otherBlock.filePath);
                const command = `command:duplicateFinder.navigateToDuplicate?${encodeURIComponent(JSON.stringify(otherBlock))}`;
                markdown.appendMarkdown(`- [${relativePath}:${otherBlock.startLine}-${otherBlock.endLine}](${command})\n`);
            }
        }
        return markdown;
    }
    dispose() {
        this.decorationType.dispose();
        this.decorations.clear();
    }
}
exports.DecorationManager = DecorationManager;
//# sourceMappingURL=decorationManager.js.map