"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuplicateProvider = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const types_1 = require("./types");
class DuplicateProvider {
    constructor() {
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
        this.duplicateGroups = [];
    }
    updateDuplicates(groups) {
        this.duplicateGroups = groups;
        this._onDidChangeTreeData.fire();
    }
    clear() {
        this.duplicateGroups = [];
        this._onDidChangeTreeData.fire();
    }
    getTreeItem(element) {
        return element;
    }
    getChildren(element) {
        if (!element) {
            // Root level - show duplicate groups
            return Promise.resolve(this.getDuplicateGroupItems());
        }
        else if (element.duplicateGroup) {
            // Group level - show individual duplicate blocks
            return Promise.resolve(this.getDuplicateBlockItems(element.duplicateGroup));
        }
        else {
            // Block level - no children
            return Promise.resolve([]);
        }
    }
    getDuplicateGroupItems() {
        return this.duplicateGroups.map((group, index) => {
            const label = `Group ${index + 1} (${group.blocks.length} duplicates, ${group.lineCount} lines)`;
            const item = new types_1.DuplicateTreeItem(label, vscode.TreeItemCollapsibleState.Expanded, group);
            // Set icon and description
            item.iconPath = new vscode.ThemeIcon('warning');
            item.description = `${Math.round(group.similarity * 100)}% similar`;
            return item;
        });
    }
    getDuplicateBlockItems(group) {
        return group.blocks.map((block, index) => {
            const fileName = path.basename(block.filePath);
            const label = `${fileName}:${block.startLine}-${block.endLine}`;
            const item = new types_1.DuplicateTreeItem(label, vscode.TreeItemCollapsibleState.None, undefined, block);
            // Set icon based on file type
            item.iconPath = this.getFileIcon(block.filePath);
            item.description = this.getRelativePath(block.filePath);
            return item;
        });
    }
    getFileIcon(filePath) {
        const extension = path.extname(filePath).toLowerCase();
        const iconMap = {
            '.js': 'symbol-method',
            '.jsx': 'symbol-method',
            '.ts': 'symbol-method',
            '.tsx': 'symbol-method',
            '.py': 'symbol-method',
            '.java': 'symbol-class',
            '.cs': 'symbol-class',
            '.cpp': 'symbol-method',
            '.c': 'symbol-method',
            '.go': 'symbol-method',
            '.rs': 'symbol-method',
            '.php': 'symbol-method'
        };
        const iconName = iconMap[extension] || 'symbol-file';
        return new vscode.ThemeIcon(iconName);
    }
    getRelativePath(filePath) {
        if (vscode.workspace.workspaceFolders) {
            return vscode.workspace.asRelativePath(filePath);
        }
        return path.basename(filePath);
    }
}
exports.DuplicateProvider = DuplicateProvider;
//# sourceMappingURL=duplicateProvider.js.map