# Example Python file with duplicate logic

def calculate_sum(a, b):
    if a < 0 or b < 0:
        raise ValueError('Negative numbers not allowed')
    result = a + b
    print(f'Calculating sum of {a} and {b}')
    return result

def process_list(data):
    processed = []
    for item in data:
        if item is not None:
            processed.append(item * 2)
    return processed

def validate_string(input_str):
    if not input_str:
        return False
    if not isinstance(input_str, str):
        return False
    if len(input_str) < 3:
        return False
    return True

# Simple assignment - should not be flagged as duplicate
config = {
    'debug': True,
    'timeout': 30
}
