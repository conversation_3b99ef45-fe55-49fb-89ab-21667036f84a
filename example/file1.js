// Example file 1 with duplicate code

function calculateSum(a, b) {
    if (a < 0 || b < 0) {
        throw new Error('Negative numbers not allowed');
    }
    const result = a + b;
    console.log(`Calculating sum of ${a} and ${b}`);
    return result;
}

function processData(data) {
    const processed = [];
    for (let i = 0; i < data.length; i++) {
        if (data[i] !== null && data[i] !== undefined) {
            processed.push(data[i] * 2);
        }
    }
    return processed;
}

function validateInput(input) {
    if (!input) {
        return false;
    }
    if (typeof input !== 'string') {
        return false;
    }
    if (input.length < 3) {
        return false;
    }
    return true;
}
