// Example file with duplicate lines

function calculateSum(a, b) {
    console.log('Starting calculation');
    const result = a + b;
    console.log('Starting calculation');
    return result;
}

function processData(data) {
    const processed = [];
    const processed = [];
    for (let i = 0; i < data.length; i++) {
        if (data[i] !== null) {
            processed.push(data[i] * 2);
        }
        if (data[i] !== null) {
            processed.push(data[i] * 2);
        }
    }
    return processed;
}

function validateInput(input) {
    if (!input) {
        return false;
    }
    if (!input) {
        return false;
    }
    return true;
}
