// Example file 2 with similar duplicate code

function calculateTotal(x, y) {
    if (x < 0 || y < 0) {
        throw new Error('Negative numbers not allowed');
    }
    const sum = x + y;
    console.log(`Calculating sum of ${x} and ${y}`);
    return sum;
}

function transformArray(arr) {
    const transformed = [];
    for (let i = 0; i < arr.length; i++) {
        if (arr[i] !== null && arr[i] !== undefined) {
            transformed.push(arr[i] * 2);
        }
    }
    return transformed;
}

function checkInput(value) {
    if (!value) {
        return false;
    }
    if (typeof value !== 'string') {
        return false;
    }
    if (value.length < 3) {
        return false;
    }
    return true;
}

// This is a unique function
function uniqueFunction() {
    console.log('This function is unique');
    return 'unique';
}
